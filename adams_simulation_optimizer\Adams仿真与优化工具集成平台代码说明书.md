# Adams仿真与优化工具集成平台代码说明书

## 1. 项目概述

Adams仿真与优化工具集成平台是一个专业的多体动力学仿真与参数优化集成工具，提供批量仿真和智能参数优化功能。该平台采用模块化设计，集成了机器学习算法和遗传算法，为工程仿真提供了完整的解决方案。

**版本信息**：v2.0.0  
**开发团队**：Adams Simulation Team  
**技术栈**：Python 3.7+, PyQt5, scikit-learn, pygad

## 2. 项目文件结构说明

### 2.1 项目总体结构图

```
adams_simulation_optimizer/                    # 项目根目录
├── main.py                                   # 主入口文件
├── run_app.py                                # 启动脚本
├── config.py                                 # 配置管理文件
├── requirements.txt                          # 依赖包清单
├── README.md                                 # 项目说明文档
├── 如何运行程序.md                            # 运行指南
├── 重构完成报告.md                            # 重构报告
├── Adams仿真与优化工具集成平台代码说明书.md      # 本文档
├── gui/                                      # 界面模块目录
│   ├── __init__.py                          # 模块初始化文件
│   ├── main_window.py                       # 主窗口模块
│   └── common_widgets.py                    # 通用组件模块
├── simulation/                               # 仿真模块目录
│   ├── __init__.py                          # 模块初始化文件
│   ├── batch_simulator.py                   # 批量仿真核心逻辑
│   └── simulation_ui.py                     # 仿真界面模块
├── optimization/                             # 优化模块目录
│   ├── __init__.py                          # 模块初始化文件
│   ├── ml_optimizer.py                      # 机器学习优化器
│   └── optimization_ui.py                   # 优化界面模块
├── utils/                                    # 工具模块目录
│   ├── __init__.py                          # 模块初始化文件
│   ├── adams_interface.py                   # Adams接口工具
│   ├── data_processor.py                    # 数据处理工具
│   └── file_manager.py                      # 文件管理工具
├── scripts/                                  # 脚本目录
│   ├── install_dependencies.py              # 依赖安装脚本
│   └── start_app.bat                        # Windows启动脚本
├── docs/                                     # 文档目录
├── tests/                                    # 测试目录
├── AdamsBatchSimulationResults/              # 仿真结果目录
└── __pycache__/                             # Python缓存目录
```

### 2.2 根目录文件说明

#### 2.2.1 主程序文件
- **main.py**：程序主入口文件，负责启动整个应用程序，集成了批量仿真和参数优化两大功能模块
- **run_app.py**：启动脚本，提供便捷的程序启动方式
- **config.py**：配置管理文件，统一管理应用程序的配置、样式、常量等

#### 2.2.2 配置文件
- **requirements.txt**：依赖包清单，包含PyQt5、numpy、pandas、scikit-learn等必要依赖
- **README.md**：项目说明文档，包含安装使用指南和技术支持信息

#### 2.2.3 文档文件
- **如何运行程序.md**：详细的程序运行指南，包含常见错误解决方案
- **重构完成报告.md**：代码重构的详细报告和技术说明
- **Adams仿真与优化工具集成平台代码说明书.md**：本技术文档

### 2.3 核心模块目录说明

#### 2.3.1 gui目录
为程序提供图形用户界面，包含如下文件：

![gui目录结构](图片/gui目录.png)

**main_window.py**：主窗口模块，提供应用程序的主界面，整合批量仿真和参数优化功能。重构自原来的integrated_ui.py文件，采用选项卡设计便于在不同功能间切换。主要功能包括：
- 界面初始化和布局管理
- 模块动态加载和错误处理
- 统一样式主题应用
- 选项卡切换和状态管理

**common_widgets.py**：通用组件模块，提供可复用的界面组件，包括自定义按钮、输入框、表格等控件，确保界面风格统一。主要组件包括：
- 自定义按钮组件（支持主题色彩）
- 参数输入组件（带验证功能）
- 进度显示组件（实时更新）
- 结果展示表格（支持排序和筛选）

#### 2.3.2 simulation目录
本目录为批量仿真功能模块，提供Adams多体动力学仿真的核心功能，目录中重要文件说明如下：

![simulation目录结构](图片/simulation目录.png)

**batch_simulator.py**：批量仿真核心逻辑模块，提供Adams批量仿真的核心功能。重构自原来的batch_simulation_logic.py文件，包含参数化仿真、实时进度显示、结果自动保存等功能。核心功能包括：
- 拉丁超立方采样参数生成
- Adams COM接口调用管理
- 仿真进度监控和状态更新
- 结果数据提取和RMS计算
- 异常处理和错误恢复

**simulation_ui.py**：仿真界面模块，提供批量仿真的用户界面，包括参数设置、工况配置、进度显示等界面元素。界面功能包括：
- 参数范围设置界面
- 工况数据输入表格
- 仿真进度实时显示
- 结果文件管理界面

**__init__.py**：模块初始化文件，定义模块的公共接口和导出函数。

#### 2.3.3 optimization目录
本目录为参数优化功能模块，使用机器学习回归模型结合遗传算法进行参数优化，目录中重要文件说明如下：

![optimization目录结构](图片/optimization目录.png)

**ml_optimizer.py**：机器学习优化器模块，使用机器学习回归模型结合遗传算法进行参数优化。重构自原来的ml_ga_optimization.py文件，支持随机森林、极端随机森林、堆叠集成等多种模型。核心算法包括：
- 多种机器学习回归模型训练
- 交叉验证和模型性能评估
- 遗传算法参数优化
- 优化结果分析和可视化

**optimization_ui.py**：优化界面模块，提供参数优化的用户界面，包括模型选择、参数配置、结果展示等功能。界面功能包括：
- 数据文件选择和预览
- 机器学习模型配置
- 优化算法参数设置
- 优化结果表格和图表展示

**__init__.py**：模块初始化文件，定义模块的公共接口和导出函数。

#### 2.3.4 utils目录
本目录为工具模块，提供各种通用功能支持，目录中重要文件说明如下：

![utils目录结构](图片/utils目录.png)

**adams_interface.py**：Adams接口工具模块，提供与Adams软件通信和数据处理的功能。重构自原来的adams_utils.py文件，包含COM对象管理、数据传输、结果处理等功能。主要功能包括：
- Excel COM对象安全管理
- Adams Socket通信接口
- 仿真结果文件处理
- 数据格式转换和验证

**data_processor.py**：数据处理模块，提供数据预处理、特征提取、误差计算等数据处理功能。主要功能包括：
- 数据清洗和预处理
- 特征工程和标准化
- 相对误差计算算法
- 统计分析和数据验证

**file_manager.py**：文件管理模块，提供Excel文件读写、路径管理、文件操作等功能。主要功能包括：
- Excel文件安全读写
- 文件路径验证和管理
- 数据备份和恢复
- 批量文件操作

**__init__.py**：模块初始化文件，定义模块的公共接口和导出函数。

#### 2.3.5 scripts目录
本目录为脚本文件夹，包含程序启动和依赖管理脚本，目录中重要文件说明如下：

![scripts目录结构](图片/scripts目录.png)

**start_app.bat**：Windows批处理启动脚本，自动检查和安装依赖，提供一键启动功能。脚本功能包括：
- Python环境检查和验证
- 关键依赖包自动检测
- 依赖缺失时自动安装
- 程序启动和错误处理
- 用户友好的错误提示

**install_dependencies.py**：依赖安装脚本，自动安装和配置程序所需的Python包，包括PyWin32的特殊配置。脚本功能包括：
- Python版本兼容性检查
- 依赖包自动安装和验证
- PyWin32特殊配置处理
- 安装进度显示和错误处理

#### 2.3.6 tests目录和测试文件
本目录包含各种测试文件，用于验证程序功能和调试问题，主要测试文件说明如下：

**test_minimal.py**：最小测试脚本，用于测试基本的模块导入和功能验证。测试内容包括：
- 配置模块导入测试
- 工具模块功能测试
- 仿真核心模块测试
- 优化模块基本功能测试

**test_no_gui.py**：无界面测试脚本，专门测试后端逻辑功能，不依赖GUI组件。测试内容包括：
- 数据处理算法测试
- 文件操作功能测试
- Adams接口通信测试
- 机器学习模型训练测试

**test_font_consistency.py**：字体一致性测试脚本，验证界面字体设置的一致性和兼容性。

**test_refactored_app.py**：重构后应用程序测试脚本，全面测试重构后的程序功能。

**test_sampling.py**：采样算法测试脚本，专门测试拉丁超立方采样算法的正确性和效率。

**quick_test.py**：快速测试脚本，用于开发过程中的快速功能验证。

#### 2.3.7 其他重要目录

**AdamsBatchSimulationResults/**：仿真结果存储目录，自动创建用于保存批量仿真的所有结果文件，包括：
- 参数组合文件（batch_params.xlsx）
- 误差分析文件（error_summary.xlsx）
- 各次仿真的详细结果子目录

**docs/**：文档目录，用于存放项目相关的技术文档、用户手册、API参考等。

**__pycache__/**：Python字节码缓存目录，由Python解释器自动生成，用于提高模块加载速度。

## 3. 重点程序文件说明

### 3.1 main.py
程序主入口文件，负责启动整个应用程序的核心控制器。

#### 主要函数说明：

**check_dependencies()**
检查必要的依赖包是否已安装，包括PyQt5、numpy、pandas、scipy、sklearn、pygad、openpyxl等。如果发现缺少依赖包，会提示用户运行安装脚本。

**setup_application()**
设置应用程序基本属性，包括高DPI支持、应用程序信息设置、全局字体配置、样式应用等。

**main()**
主函数，程序入口点，完成依赖检查、应用程序设置、主窗口创建和事件循环启动等工作。

#### 主要特性：
- 自动依赖检查和错误提示
- 高DPI显示支持
- 统一的错误处理机制
- 完善的异常捕获和日志记录

### 3.2 config.py
配置管理文件，统一管理应用程序的所有配置信息。

#### 主要配置类：

**AppConfig类**
应用程序基本配置，包括应用名称、版本信息、界面配置、文件配置、仿真配置、优化配置等。

**StyleConfig类**
样式配置类，定义与图片一致的深蓝色主题，包括主要颜色、背景颜色、边框颜色、文字颜色、状态颜色等。

**ParameterConfig类**
参数配置类，定义仿真参数名称映射、参数显示名称、默认参数范围等。包含8个接触参数的完整配置信息。

**MLConfig类**
机器学习配置类，定义模型名称映射、模型参数等机器学习相关配置。

#### 主要函数：

**get_unified_stylesheet()**
获取统一的样式表，返回完整的CSS样式定义，确保界面风格一致。

**apply_global_style(app)**
应用全局样式到QApplication对象，实现统一的界面主题。

### 3.3 simulation/batch_simulator.py
批量仿真核心逻辑模块，提供Adams批量仿真的完整功能。

#### BatchSimulator类
批量仿真器类，继承自QObject，支持信号槽机制。

**主要属性：**
- is_running：仿真运行状态标志
- should_stop：停止仿真标志
- contact_names：Adams模型中的接触名称列表

**主要方法：**

**start_simulation(save_base_path, sim_time, sim_steps)**
启动批量仿真，根据参数文件执行多组仿真，自动保存结果并计算RMS值。

**stop_simulation()**
停止当前运行的仿真任务，安全终止仿真进程。

**信号定义：**
- progress_updated：进度更新信号
- simulation_finished：仿真完成信号  
- simulation_error：仿真错误信号

### 3.4 optimization/ml_optimizer.py
机器学习优化器模块，使用机器学习回归模型结合遗传算法进行参数优化。

#### MLOptimizer类
机器学习优化器类，继承自QObject，支持信号槽机制。

**主要属性：**
- is_running：优化运行状态标志
- should_stop：停止优化标志
- model：训练好的机器学习模型
- optimization_result：优化结果

**主要方法：**

**load_data(xlsx_path)**
加载数据文件，返回特征矩阵、目标向量和参数名称列表。

**train_model(X, y, model_type, test_ratio)**
训练机器学习模型，支持随机森林、极端随机森林、堆叠集成等模型类型。

**optimize_parameters(generations, population_size)**
使用遗传算法优化参数，基于训练好的模型进行参数搜索。

**信号定义：**
- progress_updated：进度更新信号
- optimization_finished：优化完成信号
- optimization_error：优化错误信号

### 3.5 gui/main_window.py
主窗口模块，提供应用程序的主界面，整合批量仿真和参数优化功能。

#### MainWindow类
主窗口类，继承自QMainWindow，是程序运行过程中的核心控制器。

**主要方法：**

**__init__()**
MainWindow的构造方法，完成界面初始化、模块加载、事件绑定等工作。

**init_ui()**
初始化用户界面，创建选项卡、设置窗口属性、应用样式等。

**load_modules()**
加载功能模块，包括仿真模块和优化模块的动态导入和初始化。

**主要特性：**
- 选项卡式界面设计
- 模块化功能集成
- 统一的样式主题
- 完善的错误处理

## 4. 技术特性

### 4.1 架构设计
- **模块化设计**：各功能模块独立，便于维护和扩展
- **信号槽机制**：使用PyQt5信号槽实现模块间通信
- **配置统一管理**：所有配置集中在config.py中管理
- **错误处理机制**：完善的异常捕获和用户提示

### 4.2 核心算法
- **拉丁超立方采样**：用于参数空间的均匀采样
- **机器学习回归**：支持多种回归模型进行参数预测
- **遗传算法优化**：基于pygad库实现参数优化
- **RMS计算**：自动计算振动信号的均方根值

### 4.3 界面特性
- **现代化界面**：深蓝色主题，与专业软件风格一致
- **实时进度显示**：仿真和优化过程的实时状态更新
- **数据可视化**：结果表格和图表展示
- **用户友好**：完善的提示信息和错误处理

## 5. 工具模块详细说明

### 5.1 utils/adams_interface.py
Adams接口工具模块，提供与Adams软件通信和数据处理的功能。

#### 主要函数说明：

**get_excel_app()**
安全获取Excel.Application COM对象，自动处理gen_py缓存损坏问题。首次尝试使用win32.gencache.EnsureDispatch，若捕获AttributeError且提示gen_py缺失属性，则自动删除缓存目录并重试。

**send_adams_command(command, host, port)**
向Adams软件发送命令，通过Socket连接与Adams通信。支持命令执行状态检查和错误处理。

**process_simulation_results(result_path)**
处理仿真结果文件，提取振动数据并计算RMS值。支持多种数据格式的自动识别和处理。

#### Adams通信配置：
- **ADAMS_HOST**：'localhost'（Adams服务器地址）
- **ADAMS_PORT**：5002（Adams通信端口）

### 5.2 utils/data_processor.py
数据处理模块，提供数据预处理、特征提取、误差计算等功能。

#### 主要函数说明：

**calculate_relative_errors(sim_data, exp_data)**
计算仿真数据与实验数据的相对误差，支持多种误差计算方法。

**calculate_total_relative_error(errors)**
计算总体相对误差，用于优化目标函数的评估。

**prepare_ml_data(data_frame)**
为机器学习准备数据，包括特征标准化、缺失值处理、异常值检测等。

**extract_parameter_bounds(param_config)**
从参数配置中提取参数边界，用于优化算法的约束设置。

### 5.3 utils/file_manager.py
文件管理模块，提供Excel文件读写、路径管理、文件操作等功能。

#### 主要函数说明：

**load_from_excel(file_path, sheet_name)**
从Excel文件加载数据，支持多种数据格式和编码方式。

**save_to_excel(data, file_path, sheet_name)**
将数据保存到Excel文件，支持多工作表和格式设置。

**create_backup(file_path)**
创建文件备份，确保数据安全。

**validate_file_path(file_path)**
验证文件路径的有效性和权限。

## 6. 程序运行流程说明

### 6.1 程序启动流程
```mermaid
graph TD
    A[用户启动程序] --> B[start_app.bat]
    B --> C[检查Python环境]
    C --> D{Python可用?}
    D -->|否| E[显示错误信息]
    D -->|是| F[检查关键依赖]
    F --> G{PyQt5可用?}
    G -->|否| H[自动安装依赖]
    G -->|是| I[启动main.py]
    H --> J{安装成功?}
    J -->|否| K[显示安装失败]
    J -->|是| I
    I --> L[初始化应用程序]
    L --> M[创建主窗口]
    M --> N[显示界面]
```

### 6.2 批量仿真数据流程
```mermaid
graph TD
    A[用户配置参数] --> B[生成参数组合文件]
    B --> C[拉丁超立方采样]
    C --> D[创建batch_params.xlsx]
    D --> E[启动批量仿真]
    E --> F[读取参数组合]
    F --> G[调用Adams仿真]
    G --> H[等待仿真完成]
    H --> I[提取结果数据]
    I --> J[计算RMS值]
    J --> K[计算相对误差]
    K --> L[保存到error_summary.xlsx]
    L --> M{还有参数组合?}
    M -->|是| F
    M -->|否| N[仿真完成]
```

### 6.3 参数优化数据流程
```mermaid
graph TD
    A[选择数据文件] --> B[加载error_summary.xlsx]
    B --> C[数据预处理]
    C --> D[特征标准化]
    D --> E[划分训练测试集]
    E --> F[选择机器学习模型]
    F --> G[训练回归模型]
    G --> H[模型性能评估]
    H --> I[初始化遗传算法]
    I --> J[生成初始种群]
    J --> K[适应度评估]
    K --> L[选择、交叉、变异]
    L --> M[新一代种群]
    M --> N{达到终止条件?}
    N -->|否| K
    N -->|是| O[输出最优参数]
```

### 6.4 界面交互流程
```mermaid
graph TD
    A[程序启动] --> B[加载主窗口]
    B --> C[初始化选项卡]
    C --> D[用户选择功能]
    D --> E{选择批量仿真?}
    E -->|是| F[显示仿真界面]
    E -->|否| G[显示优化界面]
    F --> H[配置仿真参数]
    H --> I[启动仿真任务]
    I --> J[显示进度更新]
    J --> K[显示仿真结果]
    G --> L[选择数据文件]
    L --> M[配置优化参数]
    M --> N[启动优化任务]
    N --> O[显示优化进度]
    O --> P[显示优化结果]
```

## 7. 配置参数说明

### 7.1 仿真参数配置
程序支持8个接触参数的优化：
- **sun_stiff**：太阳轮-行星轮接触刚度 (N/mm)
- **ring_stiff**：行星轮-内齿圈接触刚度 (N/mm)
- **sun_damping**：太阳轮-行星轮接触阻尼 (N·s/mm)
- **ring_damping**：行星轮-内齿圈接触阻尼 (N·s/mm)
- **sun_exponent**：太阳轮-行星轮力指数
- **ring_exponent**：行星轮-内齿圈力指数
- **sun_dmax**：太阳轮-行星轮穿透深度 (mm)
- **ring_dmax**：行星轮-内齿圈穿透深度 (mm)

### 7.2 机器学习模型配置
- **随机森林**：n_estimators=300, random_state=42
- **极端随机森林**：n_estimators=400, random_state=42
- **堆叠集成**：cv=5, random_state=42

### 7.3 遗传算法配置
- **默认迭代次数**：100代
- **默认种群规模**：50个个体
- **测试集比例**：0.25（25%用于测试）

## 8. 使用说明

### 8.1 系统要求
- **操作系统**：Windows 10/11 (64位)
- **Python版本**：Python 3.7 或更高版本
- **内存**：建议 8GB 以上
- **Adams软件**：需要安装 MSC Adams（用于批量仿真功能）

### 8.2 启动方式
1. **推荐方式**：双击运行 `scripts/start_app.bat`
2. **命令行方式**：在项目根目录运行 `python main.py`
3. **开发调试**：运行 `python run_app.py`

### 8.3 主要功能
- **批量仿真**：多工况参数化仿真，自动生成参数组合
- **参数优化**：基于机器学习和遗传算法的智能优化
- **结果分析**：自动计算相对误差和性能指标
- **数据管理**：Excel格式的数据输入输出

## 9. 输出文件说明

### 9.1 批量仿真输出文件
- **batch_params.xlsx**：参数组合文件，包含所有仿真参数组合
- **error_summary.xlsx**：误差分析文件，包含仿真结果和误差计算
- **sim_xxx/**：各次仿真的详细结果目录

### 9.2 参数优化输出文件
- **优化结果表格**：显示各参数的最优值和性能指标
- **模型性能报告**：包含RMSE、R²等模型评估指标

## 10. 代码示例和API接口

### 10.1 批量仿真使用示例

```python
# 导入必要模块
from simulation.batch_simulator import BatchSimulator
from config import AppConfig
import pandas as pd

# 创建批量仿真器实例
simulator = BatchSimulator()

# 连接信号槽
simulator.progress_updated.connect(lambda progress, status: print(f"进度: {progress}% - {status}"))
simulator.simulation_finished.connect(lambda: print("仿真完成"))

# 启动仿真
save_path = "C:/SimulationResults"
sim_time = AppConfig.DEFAULT_SIM_TIME
sim_steps = AppConfig.DEFAULT_SIM_STEPS

simulator.start_simulation(save_path, sim_time, sim_steps)
```

### 10.2 参数优化使用示例

```python
# 导入必要模块
from optimization.ml_optimizer import MLOptimizer
from config import MLConfig

# 创建优化器实例
optimizer = MLOptimizer()

# 连接信号槽
optimizer.progress_updated.connect(lambda progress, status: print(f"优化进度: {progress}% - {status}"))
optimizer.optimization_finished.connect(lambda result: print(f"优化完成: {result}"))

# 加载数据并开始优化
data_file = "error_summary.xlsx"
X, y, param_names = optimizer.load_data(data_file)

# 训练模型
model_type = "RandomForest"
test_ratio = 0.25
model_result = optimizer.train_model(X, y, model_type, test_ratio)

# 执行优化
generations = 100
population_size = 50
optimizer.optimize_parameters(generations, population_size)
```

### 10.3 工具模块使用示例

```python
# Adams接口使用示例
from utils.adams_interface import send_adams_command, get_excel_app

# 发送Adams命令
command = "simulation single_run transient end_time=1.0 steps=1000"
result = send_adams_command(command)

# 安全获取Excel应用
excel_app = get_excel_app()
# 使用完毕后记得关闭
excel_app.Quit()

# 数据处理使用示例
from utils.data_processor import calculate_relative_errors, prepare_ml_data

# 计算相对误差
sim_data = [1.2, 1.5, 1.8]
exp_data = [1.0, 1.4, 1.7]
errors = calculate_relative_errors(sim_data, exp_data)

# 文件管理使用示例
from utils.file_manager import load_from_excel, save_to_excel

# 从Excel加载数据
data = load_from_excel("data.xlsx", "Sheet1")

# 保存数据到Excel
save_to_excel(data, "output.xlsx", "Results")
```

### 10.4 配置管理示例

```python
# 使用配置类
from config import AppConfig, StyleConfig, ParameterConfig

# 获取应用配置
app_name = AppConfig.APP_NAME
font_size = AppConfig.FONT_SIZE
window_width = AppConfig.WINDOW_WIDTH

# 获取样式配置
primary_color = StyleConfig.PRIMARY_COLOR
background_color = StyleConfig.BACKGROUND_COLOR

# 获取参数配置
param_ranges = ParameterConfig.DEFAULT_PARAM_RANGES
param_names = ParameterConfig.PARAM_NAMES
```

## 11. 故障排除

### 11.1 常见问题及解决方案

#### 问题1：ModuleNotFoundError: No module named 'config'
**原因**：没有从项目根目录运行程序
**解决方案**：
```bash
cd adams_simulation_optimizer
python main.py
```

#### 问题2：No module named 'PyQt5'
**原因**：缺少PyQt5依赖
**解决方案**：
```bash
python scripts/install_dependencies.py
# 或手动安装
pip install PyQt5
```

#### 问题3：Adams连接失败
**原因**：Adams软件未启动或端口未开启
**解决方案**：
1. 启动Adams软件
2. 确保端口5002开启
3. 检查防火墙设置

#### 问题4：Excel COM对象错误
**原因**：PyWin32配置问题或Excel未安装
**解决方案**：
```bash
pip install pywin32
python Scripts/pywin32_postinstall.py -install
```

### 11.2 调试模式和日志

程序提供了多种调试方式：

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 独立测试模块
python test_minimal.py

# 无GUI测试
python test_no_gui.py
```

### 11.3 性能优化建议

1. **内存管理**：及时释放大型数据对象
2. **并行处理**：利用多核CPU进行批量计算
3. **缓存机制**：缓存频繁访问的数据
4. **异步处理**：使用Qt线程处理耗时操作

## 12. 扩展开发指南

### 12.1 添加新的机器学习模型

```python
# 在optimization/ml_optimizer.py中添加新模型
from sklearn.ensemble import GradientBoostingRegressor

# 在MLConfig中添加配置
MODEL_PARAMS = {
    "GradientBoosting": {"n_estimators": 200, "learning_rate": 0.1}
}
```

### 12.2 添加新的仿真参数

```python
# 在config.py的ParameterConfig中添加
DEFAULT_PARAM_RANGES = {
    "new_param": {"default": 100, "min": 50, "max": 150}
}
```

### 12.3 自定义界面组件

```python
# 在gui/common_widgets.py中添加新组件
from PyQt5.QtWidgets import QWidget

class CustomWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        # 界面初始化代码
        pass
```

---

**Adams Simulation Team**
**版本：2.0.0**
**更新日期：2025年**
