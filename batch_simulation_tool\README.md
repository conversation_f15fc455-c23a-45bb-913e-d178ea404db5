# Adams 批量仿真工具

这是一个用于Adams多体动力学仿真的批量仿真工具，支持多工况参数化仿真。

## 功能特点

- **多工况支持**：支持设置多个转速和负载扭矩工况
- **参数化仿真**：使用拉丁超立方采样生成参数组合
- **实时进度显示**：显示仿真进度和状态
- **结果自动保存**：仿真结果自动保存为Excel格式
- **RMS自动计算**：自动计算振动RMS值

## 文件结构

```
batch_simulation_tool/
├── __init__.py              # 包初始化文件
├── main.py                  # 主程序入口
├── batch_simulation_ui.py   # 用户界面
├── batch_simulation_logic.py # 仿真逻辑
├── adams_utils.py           # 工具函数
└── README.md               # 说明文档
```

## 使用方法

### 1. 启动程序

```bash
python main.py
```

或者直接运行：

```bash
python batch_simulation_ui.py
```

### 2. 配置仿真参数

1. **仿真参数设置**：设置仿真时间和步数
2. **结果保存路径**：选择仿真结果保存目录
3. **参数范围设置**：设置8个接触参数的取值范围
4. **工况设置**：添加多个转速和负载扭矩工况

### 3. 生成参数文件

点击"生成参数文件"按钮，系统将：
- 使用拉丁超立方采样生成参数组合
- 与工况进行笛卡尔积运算
- 保存为 `batch_params.xlsx` 文件

### 4. 启动批量仿真

点击"启动批量仿真"按钮开始仿真，系统将：
- 逐组执行仿真
- 自动计算RMS值
- 更新结果文件
- 显示实时进度

## 参数说明

### 接触参数
- **s-p刚度(N/mm)**：太阳轮-行星轮接触刚度
- **p-r刚度(N/mm)**：行星轮-内齿圈接触刚度
- **s-p阻尼(N)**：太阳轮-行星轮接触阻尼
- **p-r阻尼(N)**：行星轮-内齿圈接触阻尼
- **s-p力指数**：太阳轮-行星轮接触力指数
- **p-r力指数**：行星轮-内齿圈接触力指数
- **s-p穿透深度(mm)**：太阳轮-行星轮接触穿透深度
- **p-r穿透深度(mm)**：行星轮-内齿圈接触穿透深度

### 工况参数
- **转速(rpm)**：输入转速，系统自动转换为deg/s
- **负载扭矩(N·m)**：输入扭矩，系统自动转换为N·mm

## 输出文件

- **batch_params.xlsx**：包含所有参数组合和仿真结果
- **sim_001/**、**sim_002/** 等：每组仿真的详细结果文件夹
  - `simulation_results_001.htm`：Adams原始结果文件
  - `simulation_results_001.xlsx`：转换后的Excel结果文件

## 系统要求

- Python 3.7+
- PyQt5
- pandas
- numpy
- scipy
- win32com (Windows系统)
- Adams 2018+ (需要开启命令服务器，端口5002)

## 注意事项

1. 确保Adams已启动并开启命令服务器
2. 确保Adams模型已加载
3. 仿真过程中请勿关闭Adams
4. 大量仿真可能需要较长时间，请耐心等待

## 版本信息

- 版本：1.0.0
- 作者：Adams Simulation Team
- 更新日期：2024年 