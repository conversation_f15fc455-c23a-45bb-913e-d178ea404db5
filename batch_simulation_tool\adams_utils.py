import os
import time
import socket
import pythoncom
import numpy as np
import pandas as pd
import win32com.client as win32
import win32com  # 用于获取 gen_py 路径
from win32com.client import constants as xl

# 定义与Adams通信的全局变量
ADAMS_HOST = 'localhost'
ADAMS_PORT = 5002

# -----------------------------------------------------------------------------
# Excel COM 获取助手：自动处理 gen_py 缓存损坏
# -----------------------------------------------------------------------------

def get_excel_app():
    """安全获取 Excel.Application COM 对象。

    1) 首次尝试使用 win32.gencache.EnsureDispatch。
    2) 若捕获 AttributeError 且提示 gen_py 缺失属性，则自动删除缓存目录并重试。
    3) 调用方仍需负责最终的 excel.Quit()。
    """

    # 先初始化 COM
    pythoncom.CoInitialize()

    try:
        return win32.gencache.EnsureDispatch('Excel.Application')
    except AttributeError as e:
        # 检测到 gen_py 相关错误，尝试清空缓存后重试
        err_msg = str(e)
        if 'gen_py' in err_msg or 'CLSID' in err_msg:
            try:
                import shutil
                shutil.rmtree(win32com.__gen_path__, ignore_errors=True)
                # 再次尝试
                return win32.gencache.EnsureDispatch('Excel.Application')
            except Exception:
                # 若仍失败，继续抛出原始异常以便外层捕获
                raise
        # 其它 AttributeError 直接抛出
        raise

# -----------------------------------------------------------------------------

def htm_to_xlsx(htm_path, xlsx_path=None):
    """
    使用win32com将HTM文件转换为XLSX文件，包含完整的COM初始化和异常处理。
    """
    htm_path = os.path.normpath(htm_path)
    if not os.path.exists(htm_path):
        raise FileNotFoundError(f"找不到文件: {htm_path}")
    if xlsx_path is None:
        xlsx_path = os.path.splitext(htm_path)[0] + '.xlsx'
    
    excel = None
    try:
        excel = get_excel_app()
        excel.Visible = False
        excel.DisplayAlerts = False
        
        print(f"正在打开HTM: {htm_path}")
        wb = excel.Workbooks.Open(htm_path)
        
        print(f"正在另存为XLSX: {xlsx_path}")
        wb.SaveAs(xlsx_path, FileFormat=51) # 51 corresponds to xlOpenXMLWorkbook
        
        wb.Close(SaveChanges=False)
        print(f"成功将 {htm_path} 转换为 {xlsx_path}")
        return xlsx_path
    except Exception as e:
        print(f"HTM转换过程中发生错误: {e}")
        raise
    finally:
        if excel:
            try:
                excel.Quit()
            except Exception as e:
                print(f"关闭Excel时出错: {e}")
        pythoncom.CoUninitialize()

def send_adams_command(cmd, host=ADAMS_HOST, port=ADAMS_PORT):
    """
    发送命令到Adams并处理连接错误。
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as client_socket:
            client_socket.connect((host, port))
            client_socket.send(cmd.encode())
            response = client_socket.recv(1024).decode()
            return response
    except ConnectionRefusedError:
        print("连接错误: 无法连接到Adams，请确保Adams已启动并开启端口5002。")
        return None
    except Exception as e:
        print(f"发送命令失败: {str(e)}")
        return None

def calculate_rms(data):
    """计算Series或list的均方根值"""
    if isinstance(data, pd.Series):
        data = data.dropna()
    if len(data) == 0:
        return np.nan
    return np.sqrt(np.mean(np.square(data)))

def analyze_vibration_data(xlsx_path, start_row=2000, column_index=1):
    """
    从 XLSX 文件中读取振动数据并计算 RMS 值。

    默认仅使用 **第二列(column_index=1)** 从 **第 2000 行(start_row) 及之后** 的数据参与 RMS 计算。

    参数:
    xlsx_path (str): Excel 文件路径。
    start_row (int): 开始行号（1-based，含表头后计算）。默认 2000。
    column_index (int): 需要读取的数据列索引（0-based）。默认 1，即第二列。
    """
    try:
        # header=1 表示跳过第一行标题行（Excel 导出的 HTM 通常第一行为空白，第二行为标题）。
        df = pd.read_excel(xlsx_path, header=1)

        # 将 1-based 行号转换为 pandas 0-based 索引。若 start_row 小于等于 0，则从头开始。
        start_idx = max(start_row - 1, 0)

        # 选取指定列并截取指定行之后的数据
        vibration_data = df.iloc[start_idx:, column_index]

        rms = calculate_rms(vibration_data)
        rms_scaled = rms / 1000.0  # 单位换算：假设原始单位为 mm/s^2，将其转换为 m/s^2

        print(
            f"文件 {os.path.basename(xlsx_path)} 从第 {start_row} 行后的 RMS 值: {rms:.4f} -> 换算后 {rms_scaled:.4f}")

        return {df.columns[column_index]: rms_scaled}
    except Exception as e:
        print(f"读取或分析 Excel 文件时出错 {xlsx_path}: {e}")
        raise

def process_simulation_results(html_file, xlsx_file):
    """
    处理单次仿真结果，包括HTM到XLSX转换和RMS计算。
    返回主RMS值。
    """
    # 1. HTM转XLSX
    try:
        htm_to_xlsx(html_file, xlsx_file)
    except Exception as e:
        # 在函数内部处理异常，并向上层抛出更具体的信息
        raise Exception(f"HTM到Excel转换失败 for {html_file}: {e}")

    # 2. 计算RMS
    try:
        rms_results = analyze_vibration_data(xlsx_file)
        if not rms_results:
             raise ValueError("无法计算RMS值，结果为空。")
        main_rms = list(rms_results.values())[0]
        return main_rms
    except Exception as e:
        raise Exception(f"RMS计算失败 for {xlsx_file}: {e}") 