#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最小测试脚本
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_imports():
    """测试导入"""
    print("🔍 测试基本导入...")
    
    try:
        print("1. 测试配置导入...")
        from config import AppConfig
        print(f"✅ 配置导入成功: {AppConfig.APP_NAME}")
        
        print("2. 测试工具导入...")
        from utils.file_manager import save_to_excel
        print("✅ 工具导入成功")
        
        print("3. 测试仿真核心导入...")
        from simulation.batch_simulator import BatchSimulator
        print("✅ 仿真核心导入成功")
        
        print("4. 测试GUI组件导入...")
        from gui.common_widgets import ProgressDialog
        print("✅ GUI组件导入成功")
        
        print("5. 测试仿真界面导入...")
        from simulation.simulation_ui import SimulationWidget
        print("✅ 仿真界面导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_imports()
