#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证重构后的应用程序
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        # 测试配置
        from config import AppConfig
        print(f"✅ 应用名称: {AppConfig.APP_NAME}")
        print(f"✅ 应用版本: {AppConfig.APP_VERSION}")
        
        # 测试工具模块
        from utils.file_manager import save_to_excel, load_from_excel
        print("✅ 文件管理工具导入成功")
        
        # 测试仿真核心
        from simulation.batch_simulator import BatchSimulator
        print("✅ 批量仿真器导入成功")
        
        # 测试优化核心
        from optimization.ml_optimizer import MLOptimizer
        print("✅ 机器学习优化器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n🔍 测试GUI组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        
        # 测试通用组件
        from gui.common_widgets import ProgressDialog, FileSelector
        print("✅ 通用界面组件导入成功")
        
        # 测试主窗口（不显示）
        from gui.main_window import MainWindow
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 检查选项卡
        tab_count = main_window.tab_widget.count()
        print(f"✅ 选项卡数量: {tab_count}")
        
        # 清理
        main_window.close()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Adams 仿真与优化工具 - 快速测试")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("GUI组件", test_gui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 重构成功！应用程序可以正常工作！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        print(f"\n退出代码: {exit_code}")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
