# Adams 仿真与优化工具 - 运行指南

## 🚀 推荐的启动方式

### 方法1：使用启动脚本（最简单）
```bash
# 双击运行
scripts/start_app.bat

# 或在命令行中运行
cd adams_simulation_optimizer
scripts\start_app.bat
```

### 方法2：使用Python启动脚本
```bash
cd adams_simulation_optimizer
python run_app.py
```

### 方法3：直接运行主程序
```bash
cd adams_simulation_optimizer
python main.py
```

## ❌ 常见错误和解决方案

### 错误1：ModuleNotFoundError: No module named 'config'
**原因**：没有从项目根目录运行程序

**解决方案**：
```bash
# 确保在正确的目录
cd adams_simulation_optimizer

# 然后运行程序
python main.py
```

### 错误2：No module named 'PyQt5'
**原因**：缺少PyQt5依赖

**解决方案**：
```bash
# 自动安装依赖
python scripts/install_dependencies.py

# 或手动安装
pip install PyQt5
```

### 错误3：直接运行子模块文件失败
**原因**：子模块文件（如gui/main_window.py）不是程序入口

**解决方案**：
- 不要直接运行子模块文件
- 始终从项目根目录运行 `main.py`

## 📁 正确的目录结构

确保您的目录结构如下：
```
adams_simulation_optimizer/          # <- 在这个目录下运行
├── main.py                         # <- 主程序入口
├── run_app.py                      # <- 启动脚本
├── config.py                       # <- 配置文件
├── gui/
│   ├── main_window.py             # <- 不要直接运行这个
│   └── ...
├── simulation/
├── optimization/
├── utils/
└── scripts/
    └── start_app.bat              # <- 启动脚本
```

## 🔧 调试模式

如果需要调试特定的界面组件，可以独立运行：

```bash
# 调试主窗口（已修复路径问题）
cd adams_simulation_optimizer
python gui/main_window.py

# 调试仿真界面
cd adams_simulation_optimizer
python -c "
import sys, os
sys.path.insert(0, '.')
from PyQt5.QtWidgets import QApplication
from simulation.simulation_ui import SimulationWidget
app = QApplication(sys.argv)
widget = SimulationWidget()
widget.show()
app.exec_()
"
```

## 💡 最佳实践

1. **始终从项目根目录运行**
2. **使用提供的启动脚本**
3. **遇到问题先检查依赖**
4. **不要直接运行子模块文件**

## 📞 获取帮助

如果仍然遇到问题：

1. 检查Python版本（需要3.7+）
2. 运行依赖检查：`python scripts/install_dependencies.py`
3. 查看详细错误信息
4. 确保在正确的目录下运行

---

**记住：始终从 `adams_simulation_optimizer` 目录运行程序！**
