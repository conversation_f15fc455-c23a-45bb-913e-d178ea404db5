#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数范围收缩 GUI

功能：
1. 选择 batch_params.xlsx、error_summary.xlsx
2. 设置阈值 & margin
3. 点击"计算"调用 refine_bounds，结果展示在表格中
4. 提供"保存为 Excel"&"复制到剪贴板"按钮（供半自动填写下一轮参数表）

若后续要与 BatchSimulationUI 紧密联动，可在此处发信号或直接写回 UI 表格。
"""

import os
import sys
import pandas as pd
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QGuiApplication
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QLineEdit, QPushButton, QFileDialog, QDoubleSpinBox, QGroupBox, QTableWidget,
    QTableWidgetItem, QMessageBox
)

matplotlib_style_applied = False
try:
    import matplotlib
    matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    matplotlib.rcParams['axes.unicode_minus'] = False
    matplotlib_style_applied = True
except Exception:
    pass

from param_range_refinement import refine_bounds


class RefineWorker(QThread):
    finished = pyqtSignal(object, str, int)
    error = pyqtSignal(str)

    def __init__(self, batch_path, summary_path, threshold, margin):
        super().__init__()
        self.batch_path = batch_path
        self.summary_path = summary_path
        self.threshold = threshold
        self.margin = margin

    def run(self):
        try:
            df = refine_bounds(self.batch_path, self.summary_path, self.threshold, self.margin)
            import pandas as pd
            good_count = int(pd.read_excel(self.summary_path)['总相对误差'].le(self.threshold).sum())
            out_path = os.path.join(os.path.dirname(self.summary_path), 'param_bounds_refined.xlsx')
            df.to_excel(out_path, index=False)
            self.finished.emit(df, out_path, good_count)
        except Exception as e:
            self.error.emit(str(e))


class RefineBoundsUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('参数范围收缩')
        self.resize(900, 600)
        self._init_ui()

        QApplication.instance().setFont(QFont('Microsoft YaHei UI', 10))

    def _init_ui(self):
        central = QWidget(); self.setCentralWidget(central)
        layout = QVBoxLayout(central)

        # 文件选择组
        file_group = QGroupBox('文件选择')
        flayout = QVBoxLayout(file_group)
        # batch_params
        h1 = QHBoxLayout()
        h1.addWidget(QLabel('batch_params.xlsx:'))
        self.batch_edit = QLineEdit(); self.batch_edit.setPlaceholderText('请选择 batch_params.xlsx')
        h1.addWidget(self.batch_edit, 1)
        btn1 = QPushButton('浏览'); btn1.clicked.connect(lambda: self._browse(self.batch_edit, '选择 batch_params.xlsx'))
        h1.addWidget(btn1)
        flayout.addLayout(h1)
        # error_summary
        h2 = QHBoxLayout()
        h2.addWidget(QLabel('error_summary.xlsx:'))
        self.summary_edit = QLineEdit(); self.summary_edit.setPlaceholderText('请选择 error_summary.xlsx')
        h2.addWidget(self.summary_edit, 1)
        btn2 = QPushButton('浏览'); btn2.clicked.connect(lambda: self._browse(self.summary_edit, '选择 error_summary.xlsx'))
        h2.addWidget(btn2)
        flayout.addLayout(h2)
        layout.addWidget(file_group)

        # 参数组
        param_group = QGroupBox('参数')
        playout = QHBoxLayout(param_group)
        playout.addWidget(QLabel('阈值 (Total Error ≤):'))
        self.thr_spin = QDoubleSpinBox(); self.thr_spin.setRange(0.0, 1e9); self.thr_spin.setDecimals(3); self.thr_spin.setValue(2.0)
        playout.addWidget(self.thr_spin)
        playout.addWidget(QLabel('Margin:'))
        self.margin_spin = QDoubleSpinBox(); self.margin_spin.setRange(0.0, 1.0); self.margin_spin.setSingleStep(0.05); self.margin_spin.setValue(0.1)
        playout.addWidget(self.margin_spin)
        playout.addStretch(1)
        layout.addWidget(param_group)

        # 结果表
        self.table = QTableWidget(0, 5)
        self.table.setHorizontalHeaderLabels(['参数', 'orig_low', 'orig_high', 'new_low', 'new_high'])
        self.table.horizontalHeader().setStretchLastSection(True)
        layout.addWidget(self.table, 1)

        # 按钮
        btn_layout = QHBoxLayout()
        self.run_btn = QPushButton('计算')
        self.run_btn.clicked.connect(self._run)
        self.copy_btn = QPushButton('复制新范围')
        self.copy_btn.setEnabled(False)
        self.copy_btn.clicked.connect(self._copy_bounds)
        btn_layout.addStretch(1); btn_layout.addWidget(self.copy_btn); btn_layout.addWidget(self.run_btn)
        layout.addLayout(btn_layout)

    # ---------- slots ----------
    def _browse(self, line_edit: QLineEdit, title: str):
        path, _ = QFileDialog.getOpenFileName(self, title, os.getcwd(), 'Excel Files (*.xlsx)')
        if path:
            line_edit.setText(path)

    def _run(self):
        batch_p = self.batch_edit.text().strip()
        summary_p = self.summary_edit.text().strip()
        if not (os.path.exists(batch_p) and os.path.exists(summary_p)):
            QMessageBox.warning(self, '提示', '请正确选择两个 Excel 文件！'); return
        self.run_btn.setEnabled(False)
        self.statusBar().showMessage('正在计算...')
        self.worker = RefineWorker(batch_p, summary_p, self.thr_spin.value(), self.margin_spin.value())
        self.worker.finished.connect(self._on_finished)
        self.worker.error.connect(self._on_error)
        self.worker.start()

    def _on_finished(self, df: pd.DataFrame, out_path: str, good_count: int):
        self.run_btn.setEnabled(True)
        self.copy_btn.setEnabled(True)
        self.statusBar().showMessage(f'完成！结果已保存至 {out_path}')
        self.latest_df = df
        self.table.setRowCount(len(df))
        for row, (_, r) in enumerate(df.iterrows()):
            for col, key in enumerate(['参数', 'orig_low', 'orig_high', 'new_low', 'new_high']):
                self.table.setItem(row, col, QTableWidgetItem(f"{r[key]:.6g}" if isinstance(r[key], (int, float)) else str(r[key])))
        self.table.resizeColumnsToContents()
        QMessageBox.information(self, '完成', f'满足阈值 (≤{self.thr_spin.value():.3g}) 的参数组合数量: {good_count}')

    def _on_error(self, msg):
        self.run_btn.setEnabled(True)
        QMessageBox.critical(self, '错误', msg)
        self.statusBar().showMessage('发生错误')

    def _copy_bounds(self):
        if not hasattr(self, 'latest_df'):
            return
        df = self.latest_df[['参数', 'new_low', 'new_high']]
        text = df.to_csv(index=False, header=False)
        QGuiApplication.clipboard().setText(text)
        QMessageBox.information(self, '已复制', '已将参数新上下限复制到剪贴板，可在主界面粘贴。')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ui = RefineBoundsUI()
    ui.show()
    sys.exit(app.exec_()) 