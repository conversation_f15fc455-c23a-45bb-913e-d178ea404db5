#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本

自动检查和安装Adams仿真与优化工具所需的依赖包。
重构自原来的 check_dependencies.py 文件。

作者: Adams Simulation Team
版本: 2.0.0
"""

import sys
import subprocess
import importlib
import os


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_and_install_package(package_name, import_name=None, pip_name=None):
    """
    检查并安装包
    
    Args:
        package_name: 显示名称
        import_name: 导入时使用的名称
        pip_name: pip安装时使用的名称
    """
    if import_name is None:
        import_name = package_name.lower()
    if pip_name is None:
        pip_name = package_name.lower()
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安装")
        return True
    except ImportError:
        print(f"❌ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
            print(f"✅ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安装失败")
            return False


def main():
    """主函数"""
    print("=" * 60)
    print("Adams 仿真与优化工具 - 依赖检查和安装")
    print("=" * 60)
    print()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    print("\n正在检查必需的依赖包...")
    print("-" * 40)
    
    # 必需的依赖包
    dependencies = [
        ("PyQt5", "PyQt5", "PyQt5"),
        ("NumPy", "numpy", "numpy"),
        ("Pandas", "pandas", "pandas"),
        ("SciPy", "scipy", "scipy"),
        ("Scikit-learn", "sklearn", "scikit-learn"),
        ("OpenPyXL", "openpyxl", "openpyxl"),
        ("PyWin32", "pythoncom", "pywin32"),  # Windows COM 支持
        ("PyGAD", "pygad", "pygad"),  # 遗传算法库
    ]
    
    failed_packages = []
    
    for package_name, import_name, pip_name in dependencies:
        if not check_and_install_package(package_name, import_name, pip_name):
            failed_packages.append(package_name)
    
    print("\n" + "=" * 60)
    print("检查结果")
    print("=" * 60)
    
    if not failed_packages:
        print("✅ 所有依赖包都已成功安装！")
        print("\n现在可以运行程序了：")
        print("  python main.py")
        
        # 特别处理 PyWin32 的后安装步骤
        print("\n正在配置PyWin32...")
        try:
            import pythoncom
            print("✅ PyWin32 配置正常")
        except ImportError:
            print("⚠️  PyWin32 需要额外配置")
            print("请尝试运行以下命令：")
            print("  python -m pip install --upgrade pywin32")
            try:
                # 尝试运行后安装脚本
                scripts_dir = os.path.join(sys.prefix, "Scripts")
                postinstall_script = os.path.join(scripts_dir, "pywin32_postinstall.py")
                if os.path.exists(postinstall_script):
                    subprocess.check_call([sys.executable, postinstall_script, "-install"])
                    print("✅ PyWin32 后安装配置完成")
                else:
                    print("⚠️  找不到PyWin32后安装脚本，请手动配置")
            except Exception as e:
                print(f"⚠️  PyWin32后安装配置失败: {e}")
        
        return 0
    else:
        print(f"❌ 以下包安装失败: {', '.join(failed_packages)}")
        print("\n请手动安装这些包：")
        for package in failed_packages:
            # 找到对应的pip名称
            for pkg_name, _, pip_name in dependencies:
                if pkg_name == package:
                    print(f"  pip install {pip_name}")
                    break
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中发生错误: {e}")
        input("\n按回车键退出...")
        sys.exit(1)
