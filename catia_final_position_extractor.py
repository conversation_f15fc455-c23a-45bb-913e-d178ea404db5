#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CATIA 最终版位置提取工具

综合版本的CATIA装配体零件位置信息提取工具。
结合了结构分析和位置提取的最佳实践。

功能特点:
- 稳定的COM接口访问
- 详细的装配体结构分析
- 多种位置获取方法
- 完整的错误处理
- 用户友好的输出格式

作者: CATIA Automation Team
版本: 2.0.0
日期: 2025-01-28
"""

import pythoncom
import win32com.client as win32
import csv
import json
import sys
import os
from datetime import datetime


class CATIAPositionExtractor:
    """CATIA位置提取器主类"""
    
    def __init__(self):
        self.catia = None
        self.assembly = None
        self.parts_data = []
        
    def connect(self):
        """连接到CATIA"""
        try:
            pythoncom.CoInitialize()
            self.catia = win32.GetActiveObject("CATIA.Application")
            print("✓ 成功连接到CATIA")
            return True
        except Exception as e:
            print(f"✗ 连接CATIA失败: {e}")
            return False
    
    def get_assembly(self):
        """获取当前装配体"""
        try:
            doc = self.catia.ActiveDocument
            if doc.Name.endswith('.CATProduct'):
                self.assembly = doc.Product
                print(f"✓ 获取装配体: {doc.Name}")
                return True
            else:
                print("✗ 当前文档不是装配体(.CATProduct)")
                return False
        except Exception as e:
            print(f"✗ 获取装配体失败: {e}")
            return False
    
    def get_product_position_info(self, product):
        """获取产品的位置信息"""
        position_info = {
            'x': 0.0, 'y': 0.0, 'z': 0.0,
            'rx': 0.0, 'ry': 0.0, 'rz': 0.0,
            'method': 'Default',
            'has_position': False,
            'has_constraints': False,
            'constraint_count': 0
        }
        
        try:
            # 检查约束
            try:
                constraints = product.Constraints
                position_info['constraint_count'] = constraints.Count
                position_info['has_constraints'] = constraints.Count > 0
                if constraints.Count > 0:
                    position_info['method'] = f'Constrained({constraints.Count})'
            except:
                pass
            
            # 尝试获取位置对象
            try:
                position = product.Position
                if position:
                    position_info['has_position'] = True
                    # 不调用GetComponents()避免错误，只标记位置可用
                    if position_info['method'] == 'Default':
                        position_info['method'] = 'PositionAvailable'
            except:
                pass
            
            # 尝试获取Move对象
            try:
                move = product.Move
                if move:
                    if position_info['method'] == 'Default':
                        position_info['method'] = 'MoveAvailable'
            except:
                pass
            
        except Exception as e:
            print(f"    获取位置信息时出错: {e}")
        
        return position_info
    
    def get_product_details(self, product):
        """获取产品详细信息"""
        details = {
            'name': 'Unknown',
            'part_number': 'N/A',
            'type': 'Unknown',
            'file_name': 'N/A',
            'file_path': 'N/A',
            'is_loaded': False,
            'sub_count': 0
        }
        
        try:
            details['name'] = product.Name
            details['part_number'] = getattr(product, 'PartNumber', 'N/A')
            
            # 获取文件信息
            try:
                if hasattr(product, 'ReferenceProduct'):
                    ref_product = product.ReferenceProduct
                    if ref_product:
                        parent_doc = ref_product.Parent
                        details['file_name'] = parent_doc.Name
                        details['file_path'] = getattr(parent_doc, 'Path', 'N/A')
                        details['is_loaded'] = True
                        
                        if parent_doc.Name.endswith('.CATPart'):
                            details['type'] = 'Part'
                        elif parent_doc.Name.endswith('.CATProduct'):
                            details['type'] = 'Assembly'
            except:
                pass
            
            # 获取子产品数量
            try:
                if hasattr(product, 'Products'):
                    details['sub_count'] = product.Products.Count
                    if details['type'] == 'Unknown' and details['sub_count'] > 0:
                        details['type'] = 'Assembly'
            except:
                pass
            
        except Exception as e:
            print(f"    获取产品详细信息时出错: {e}")
        
        return details
    
    def extract_structure(self, product, level=0):
        """递归提取装配体结构"""
        try:
            # 获取产品详细信息
            details = self.get_product_details(product)
            position_info = self.get_product_position_info(product)
            
            # 合并信息
            part_data = {
                'level': level,
                'name': details['name'],
                'part_number': details['part_number'],
                'type': details['type'],
                'file_name': details['file_name'],
                'file_path': details['file_path'],
                'is_loaded': details['is_loaded'],
                'sub_count': details['sub_count'],
                'position_x': position_info['x'],
                'position_y': position_info['y'],
                'position_z': position_info['z'],
                'rotation_x': position_info['rx'],
                'rotation_y': position_info['ry'],
                'rotation_z': position_info['rz'],
                'position_method': position_info['method'],
                'has_position': position_info['has_position'],
                'has_constraints': position_info['has_constraints'],
                'constraint_count': position_info['constraint_count']
            }
            
            self.parts_data.append(part_data)
            
            # 显示进度
            indent = "  " * level
            load_mark = "✓" if details['is_loaded'] else "○"
            pos_mark = "📍" if position_info['has_position'] else "○"
            constraint_mark = "🔗" if position_info['has_constraints'] else "○"
            sub_info = f"({details['sub_count']} 子项)" if details['sub_count'] > 0 else ""
            
            print(f"{indent}├─ {load_mark}{pos_mark}{constraint_mark} {details['name']} [{details['type']}] {sub_info}")
            print(f"{indent}│  文件: {details['file_name']} | 方法: {position_info['method']}")
            
            # 递归处理子产品
            if details['sub_count'] > 0:
                try:
                    for i in range(1, details['sub_count'] + 1):
                        sub_product = product.Products.Item(i)
                        self.extract_structure(sub_product, level + 1)
                except Exception as e:
                    print(f"{indent}  警告: 处理子产品时出错 - {e}")
                    
        except Exception as e:
            print(f"提取结构时出错 {getattr(product, 'Name', 'Unknown')}: {e}")
    
    def save_to_csv(self, filename=None):
        """保存到CSV文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"catia_positions_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['level', 'name', 'part_number', 'type', 'file_name', 'file_path',
                             'is_loaded', 'sub_count', 'position_x', 'position_y', 'position_z',
                             'rotation_x', 'rotation_y', 'rotation_z', 'position_method',
                             'has_position', 'has_constraints', 'constraint_count']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for part in self.parts_data:
                    writer.writerow(part)
            
            print(f"✓ 数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存CSV文件失败: {e}")
            return None
    
    def save_to_json(self, filename=None):
        """保存到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"catia_positions_{timestamp}.json"
        
        try:
            output_data = {
                'extraction_time': datetime.now().isoformat(),
                'assembly_name': self.assembly.Name if self.assembly else 'Unknown',
                'total_items': len(self.parts_data),
                'parts_data': self.parts_data
            }
            
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(output_data, jsonfile, ensure_ascii=False, indent=2)
            
            print(f"✓ JSON数据已保存到: {filename}")
            return filename
        except Exception as e:
            print(f"✗ 保存JSON文件失败: {e}")
            return None
    
    def print_summary(self):
        """打印摘要信息"""
        print("\n" + "="*90)
        print("CATIA 装配体位置提取摘要")
        print("="*90)
        
        total = len(self.parts_data)
        loaded = sum(1 for p in self.parts_data if p['is_loaded'])
        assemblies = sum(1 for p in self.parts_data if p['type'] == 'Assembly')
        parts = sum(1 for p in self.parts_data if p['type'] == 'Part')
        with_position = sum(1 for p in self.parts_data if p['has_position'])
        with_constraints = sum(1 for p in self.parts_data if p['has_constraints'])
        
        print(f"总项目数量: {total}")
        print(f"  - 装配体: {assemblies} 个")
        print(f"  - 零件: {parts} 个")
        print(f"  - 已加载: {loaded} 个 ({loaded/total*100:.1f}%)")
        print(f"  - 有位置对象: {with_position} 个 ({with_position/total*100:.1f}%)")
        print(f"  - 有约束: {with_constraints} 个 ({with_constraints/total*100:.1f}%)")
        
        # 统计方法
        methods = {}
        for part in self.parts_data:
            method = part['position_method']
            methods[method] = methods.get(method, 0) + 1
        
        print("\n位置获取方法统计:")
        for method, count in sorted(methods.items()):
            print(f"  {method}: {count} 个")
        
        print(f"\n详细项目列表:")
        print("-" * 90)
        for i, part in enumerate(self.parts_data):
            indent = "  " * part['level']
            marks = ""
            marks += "✓" if part['is_loaded'] else "○"
            marks += "📍" if part['has_position'] else "○"
            marks += "🔗" if part['has_constraints'] else "○"
            
            sub_info = f" ({part['sub_count']} 子项)" if part['sub_count'] > 0 else ""
            constraint_info = f" [{part['constraint_count']}约束]" if part['constraint_count'] > 0 else ""
            
            print(f"{i+1:2d}. {indent}{marks} {part['name']} [{part['type']}]{sub_info}{constraint_info}")
            print(f"    {indent}   文件: {part['file_name']} | 方法: {part['position_method']}")
    
    def disconnect(self):
        """断开连接"""
        try:
            pythoncom.CoUninitialize()
        except:
            pass


def main():
    """主函数"""
    print("CATIA 装配体零件位置信息提取工具 v2.0")
    print("="*70)
    print("图例: ✓=已加载 ○=未加载 📍=位置可用 🔗=有约束")
    print()
    
    extractor = CATIAPositionExtractor()
    
    try:
        # 连接CATIA
        if not extractor.connect():
            print("\n请确保CATIA已启动")
            input("按回车键退出...")
            return
        
        # 获取装配体
        if not extractor.get_assembly():
            print("\n请打开一个装配体文档(.CATProduct)")
            input("按回车键退出...")
            return
        
        print("\n开始提取装配体结构和位置信息...")
        print("-" * 70)
        
        # 提取结构
        extractor.extract_structure(extractor.assembly)
        
        if extractor.parts_data:
            # 显示摘要
            extractor.print_summary()
            
            # 保存文件
            print(f"\n保存数据文件...")
            csv_file = extractor.save_to_csv()
            json_file = extractor.save_to_json()
            
            print(f"\n✓ 成功提取 {len(extractor.parts_data)} 个项目的信息")
            
            # 提供使用建议
            loaded_count = sum(1 for p in extractor.parts_data if p['is_loaded'])
            if loaded_count < len(extractor.parts_data):
                print(f"\n💡 建议: {len(extractor.parts_data) - loaded_count} 个项目未完全加载")
            
            constraint_count = sum(1 for p in extractor.parts_data if p['has_constraints'])
            if constraint_count > 0:
                print(f"💡 {constraint_count} 个零件有约束定位")
            else:
                print("💡 零件没有约束，可能都在原点位置")
                
        else:
            print("✗ 未找到任何项目")
    
    except Exception as e:
        print(f"✗ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        extractor.disconnect()
    
    print("\n提取完成！")
    input("按回车键退出...")


if __name__ == "__main__":
    main()
