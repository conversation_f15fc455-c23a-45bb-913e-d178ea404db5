#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序启动脚本

这个脚本确保从正确的目录启动应用程序，
并处理路径和依赖问题。

作者: Adams Simulation Team
版本: 2.0.0
"""

import sys
import os

def main():
    """主函数"""
    print("=" * 60)
    print("Adams 仿真与优化工具集成平台 v2.0")
    print("=" * 60)
    print()
    
    # 获取脚本所在目录（项目根目录）
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 切换到项目根目录
    os.chdir(project_root)
    print(f"📁 工作目录: {project_root}")
    
    # 添加项目根目录到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("❌ 错误：需要Python 3.7或更高版本")
            print(f"当前版本: {sys.version}")
            return 1
        
        print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        
        # 检查关键依赖
        print("🔍 检查关键依赖...")
        try:
            import PyQt5
            print("✅ PyQt5 可用")
        except ImportError:
            print("❌ PyQt5 未安装")
            print("💡 请运行: python scripts/install_dependencies.py")
            return 1
        
        # 导入并运行主程序
        print("🚀 启动应用程序...")
        from main import main as app_main
        return app_main()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断启动")
        return 1
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n💡 解决建议:")
        print("1. 确保所有依赖都已安装: python scripts/install_dependencies.py")
        print("2. 检查Python环境是否正确")
        print("3. 尝试重新下载项目文件")
        
        import traceback
        print(f"\n详细错误信息:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"严重错误: {e}")
        sys.exit(1)
