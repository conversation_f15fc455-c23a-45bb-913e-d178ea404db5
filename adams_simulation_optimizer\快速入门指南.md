# Adams仿真与优化工具集成平台 - 快速入门指南

## 🚀 5分钟快速上手

### 第一步：启动程序
```bash
# 方法1：双击启动脚本（推荐）
双击 scripts/start_app.bat

# 方法2：命令行启动
cd adams_simulation_optimizer
python main.py
```

### 第二步：选择功能模块
程序启动后会看到两个主要选项卡：
- **批量仿真**: 用于执行多参数组合的Adams仿真
- **参数优化**: 基于仿真结果进行智能参数优化

## 📊 批量仿真功能使用

### 1. 基本流程
```
配置参数范围 → 生成参数文件 → 启动仿真 → 查看结果
```

### 2. 详细步骤

#### 步骤1：设置保存路径
- 点击"浏览"按钮选择结果保存目录
- 建议创建专门的文件夹存放仿真结果

#### 步骤2：配置仿真参数
设置8个接触参数的范围：
- **sun_stiff**: 太阳轮-行星轮接触刚度 (N/mm)
- **ring_stiff**: 行星轮-内齿圈接触刚度 (N/mm)  
- **sun_damping**: 太阳轮-行星轮接触阻尼 (N·s/mm)
- **ring_damping**: 行星轮-内齿圈接触阻尼 (N·s/mm)
- **sun_exponent**: 太阳轮-行星轮力指数
- **ring_exponent**: 行星轮-内齿圈力指数
- **sun_dmax**: 太阳轮-行星轮穿透深度 (mm)
- **ring_dmax**: 行星轮-内齿圈穿透深度 (mm)

#### 步骤3：输入工况数据
在表格中输入：
- **转速 (rpm)**: 驱动转速
- **负载扭矩 (N·m)**: 施加的负载
- **试验RMS值**: 实测的振动RMS值（用于误差计算）

#### 步骤4：生成参数文件
- 设置采样数量（建议20-50组）
- 点击"生成参数文件"
- 系统会使用拉丁超立方采样生成参数组合

#### 步骤5：启动仿真
- 确保Adams软件已启动并开启端口5002
- 点击"启动批量仿真"
- 观察进度条和状态信息

#### 步骤6：计算误差
- 仿真完成后点击"计算相对误差"
- 系统会自动计算仿真值与试验值的相对误差
- 结果保存在error_summary.xlsx文件中

### 3. 输出文件说明
- **batch_params.xlsx**: 参数组合文件
- **error_summary.xlsx**: 误差分析结果
- **sim_001/, sim_002/, ...**: 各次仿真的详细结果

## 🧠 参数优化功能使用

### 1. 基本流程
```
选择数据文件 → 配置优化参数 → 训练模型 → 执行优化 → 查看结果
```

### 2. 详细步骤

#### 步骤1：选择数据文件
- 点击"选择文件"按钮
- 选择批量仿真生成的error_summary.xlsx文件

#### 步骤2：配置优化参数
- **拟合模型**: 选择机器学习模型
  - 随机森林 (推荐)
  - 极端随机森林
  - 堆叠集成
- **测试集比例**: 设置训练/测试数据分割比例 (建议0.25)
- **迭代次数**: 遗传算法迭代次数 (建议100)
- **种群规模**: 遗传算法种群大小 (建议50)

#### 步骤3：开始优化
- 点击"开始优化"按钮
- 系统会依次执行：
  1. 数据预处理
  2. 模型训练
  3. 性能评估
  4. 遗传算法优化

#### 步骤4：查看结果
优化完成后会显示：
- **最优参数值**: 8个参数的最优组合
- **性能指标**: RMSE、R²等模型评估指标
- **优化历史**: 优化过程的收敛曲线

## ⚙️ 高级设置

### 仿真参数调优
```python
# 在config.py中修改默认设置
DEFAULT_SIM_TIME = 0.5      # 仿真时间(秒)
DEFAULT_SIM_STEPS = 10000   # 仿真步数
DEFAULT_SAMPLE_COUNT = 20   # 默认采样数量
```

### 优化算法调优
```python
# 遗传算法参数
DEFAULT_GENERATIONS = 100      # 迭代次数
DEFAULT_POPULATION_SIZE = 50   # 种群规模
DEFAULT_TEST_RATIO = 0.25      # 测试集比例
```

## 🔧 常见问题解决

### Q1: 程序启动失败
**解决方案**:
```bash
# 检查Python环境
python --version

# 安装依赖
python scripts/install_dependencies.py

# 从正确目录启动
cd adams_simulation_optimizer
python main.py
```

### Q2: Adams连接失败
**解决方案**:
1. 确保Adams软件已启动
2. 检查端口5002是否开启
3. 确认防火墙设置允许连接

### Q3: 仿真结果异常
**检查项目**:
- Adams模型文件是否正确
- 参数范围是否合理
- 工况数据是否有效

### Q4: 优化结果不理想
**改进建议**:
- 增加仿真样本数量
- 调整参数搜索范围
- 尝试不同的机器学习模型
- 增加优化迭代次数

## 📈 最佳实践

### 1. 仿真设置建议
- **采样数量**: 根据参数维度，建议20-100组
- **仿真时间**: 确保系统达到稳态，通常0.5-2.0秒
- **参数范围**: 基于工程经验设置合理范围

### 2. 优化策略建议
- **数据质量**: 确保仿真数据的准确性和完整性
- **模型选择**: 随机森林通常表现较好，数据量大时可尝试堆叠集成
- **参数调优**: 先用较少迭代次数快速测试，再增加迭代次数精细优化

### 3. 结果验证建议
- **交叉验证**: 使用不同的测试集验证模型性能
- **工程验证**: 将优化结果与工程经验对比
- **敏感性分析**: 分析各参数对结果的影响程度

## 📞 技术支持

### 获取帮助的步骤
1. 查看本快速入门指南
2. 阅读详细的技术文档
3. 运行测试脚本检查环境
4. 查看程序运行日志
5. 联系技术支持团队

### 有用的测试命令
```bash
# 基本功能测试
python test_minimal.py

# 无界面功能测试  
python test_no_gui.py

# 依赖检查
python scripts/install_dependencies.py
```

---

**🎯 记住：从简单开始，逐步掌握高级功能！**

**Adams Simulation Team**  
**版本: 2.0.0**  
**更新日期: 2025年**
