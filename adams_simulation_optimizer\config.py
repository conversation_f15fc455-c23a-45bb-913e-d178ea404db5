#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Adams 仿真与优化工具 - 配置文件

统一管理应用程序的配置、样式、常量等。
整合了原来的 shared_styles.py 和其他配置信息。

作者: Adams Simulation Team
版本: 2.0.0
"""

from PyQt5.QtWidgets import QApplication
from PyQt5.QtGui import QFont


class AppConfig:
    """应用程序配置类"""
    
    # 应用程序基本信息
    APP_NAME = "Adams 仿真与优化工具集成平台"
    APP_VERSION = "2.0.0"
    ORGANIZATION = "Adams Simulation Team"
    
    # 界面配置
    FONT_FAMILY = "Microsoft YaHei UI"
    FONT_SIZE = 11
    WINDOW_WIDTH = 1400
    WINDOW_HEIGHT = 900
    
    # 文件配置
    BATCH_PARAMS_FILE = "batch_params.xlsx"
    ERROR_SUMMARY_FILE = "error_summary.xlsx"
    
    # 仿真配置
    DEFAULT_SIM_TIME = 0.5
    DEFAULT_SIM_STEPS = 10000
    DEFAULT_SAMPLE_COUNT = 20
    
    # 优化配置
    DEFAULT_TEST_RATIO = 0.25
    DEFAULT_GENERATIONS = 100
    DEFAULT_POPULATION_SIZE = 50


class StyleConfig:
    """样式配置类 - 与图片一致的深蓝色主题"""
    
    # 主要颜色
    PRIMARY_COLOR = '#0078D4'           # 微软蓝色 (与图片按钮颜色一致)
    PRIMARY_HOVER = '#106EBE'           # 悬停颜色
    PRIMARY_PRESSED = '#005A9E'         # 按下颜色
    
    # 背景颜色
    BACKGROUND_COLOR = '#F3F2F1'        # 主背景 (与图片背景一致)
    SURFACE_COLOR = '#FFFFFF'           # 表面色
    ALTERNATE_BG = '#FAF9F8'            # 交替背景
    
    # 边框颜色
    BORDER_COLOR = '#EDEBE9'            # 主边框
    BORDER_LIGHT = '#E1DFDD'            # 浅边框
    
    # 文字颜色
    TEXT_PRIMARY = '#323130'            # 主要文字 (与图片文字颜色一致)
    TEXT_SECONDARY = '#605E5C'          # 次要文字
    TEXT_DISABLED = '#A19F9D'           # 禁用文字
    
    # 状态颜色
    SUCCESS_COLOR = '#107C10'           # 成功
    WARNING_COLOR = '#FF8C00'           # 警告
    ERROR_COLOR = '#D13438'             # 错误


class ParameterConfig:
    """参数配置类"""
    
    # 仿真参数名称映射
    PARAM_NAMES = {
        "sun_stiff": "s-p刚度(N/mm)",
        "ring_stiff": "p-r刚度(N/mm)", 
        "sun_damping": "s-p阻尼(N)",
        "ring_damping": "p-r阻尼(N)",
        "sun_exponent": "s-p力指数",
        "ring_exponent": "p-r力指数",
        "sun_dmax": "s-p穿透深度(mm)",
        "ring_dmax": "p-r穿透深度(mm)"
    }
    
    # 参数显示名称（用于优化结果）
    PARAM_DISPLAY_NAMES = {
        "sun_stiff": "接触刚度（s-p） (N/mm)",
        "ring_stiff": "接触刚度（r-p） (N/mm)",
        "sun_damping": "接触阻尼（s-p） (N·s/mm)",
        "ring_damping": "接触阻尼（r-p） (N·s/mm)",
        "sun_exponent": "力指数（s-p）",
        "ring_exponent": "力指数（r-p）",
        "sun_dmax": "穿透深度（s-p） (mm)",
        "ring_dmax": "穿透深度（r-p） (mm)"
    }
    
    # 默认参数范围
    DEFAULT_PARAM_RANGES = {
        "sun_stiff": {"default": 380000, "min": 360000, "max": 400000},
        "ring_stiff": {"default": 760000, "min": 740000, "max": 780000},
        "sun_damping": {"default": 25, "min": 0, "max": 50},
        "ring_damping": {"default": 40, "min": 0, "max": 80},
        "sun_exponent": {"default": 2.25, "min": 1.50, "max": 3.00},
        "ring_exponent": {"default": 2.25, "min": 1.50, "max": 3.00},
        "sun_dmax": {"default": 0.125, "min": 0.050, "max": 0.200},
        "ring_dmax": {"default": 0.125, "min": 0.050, "max": 0.200}
    }


class MLConfig:
    """机器学习配置类"""
    
    # 模型名称映射
    MODEL_NAMES = {
        "随机森林": "RandomForest",
        "极端随机森林": "ExtraTrees", 
        "堆叠集成": "Stack"
    }
    
    # 模型参数
    MODEL_PARAMS = {
        "RandomForest": {"n_estimators": 300, "random_state": 42},
        "ExtraTrees": {"n_estimators": 400, "random_state": 42},
        "Stack": {"cv": 5, "random_state": 42}
    }


def get_unified_stylesheet():
    """获取统一的样式表"""
    return f"""
/* 主窗口背景 */
QMainWindow {{
    background-color: {StyleConfig.BACKGROUND_COLOR};
}}

/* 按钮样式 - 与图片一致的深蓝色主题 */
QPushButton {{
    background-color: {StyleConfig.PRIMARY_COLOR};
    color: {StyleConfig.SURFACE_COLOR};
    border: 1px solid {StyleConfig.PRIMARY_COLOR};
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
    min-height: 20px;
}}

QPushButton:hover {{
    background-color: {StyleConfig.PRIMARY_HOVER};
    border-color: {StyleConfig.PRIMARY_HOVER};
}}

QPushButton:pressed {{
    background-color: {StyleConfig.PRIMARY_PRESSED};
    border-color: {StyleConfig.PRIMARY_PRESSED};
}}

QPushButton:disabled {{
    background-color: #6C757D;
    border-color: #6C757D;
    color: {StyleConfig.TEXT_DISABLED};
}}

/* 分组框样式 */
QGroupBox {{
    border: 1px solid {StyleConfig.BORDER_COLOR};
    border-radius: 4px;
    margin-top: 15px;
    padding-top: 20px;
    font-weight: bold;
}}

QGroupBox:title {{
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    color: {StyleConfig.PRIMARY_COLOR};
    font-weight: bold;
}}

/* 输入控件样式 */
QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
    border: 1px solid {StyleConfig.BORDER_LIGHT};
    border-radius: 4px;
    padding: 4px;
    background-color: {StyleConfig.SURFACE_COLOR};
    min-height: 16px;
}}

QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
    border-color: {StyleConfig.PRIMARY_COLOR};
    outline: none;
}}

/* 表格样式 */
QTableWidget {{
    border: 1px solid {StyleConfig.BORDER_LIGHT};
    background-color: {StyleConfig.SURFACE_COLOR};
    alternate-background-color: {StyleConfig.ALTERNATE_BG};
    gridline-color: {StyleConfig.BORDER_COLOR};
    font-size: {AppConfig.FONT_SIZE}pt;
}}

QTableWidget::item {{
    font-size: {AppConfig.FONT_SIZE}pt;
}}

/* 水平表头（列标题）- 使用深蓝色背景，白色文字 */
QHeaderView::section:horizontal {{
    background-color: {StyleConfig.PRIMARY_COLOR};
    color: {StyleConfig.SURFACE_COLOR};
    border: 1px solid {StyleConfig.BORDER_LIGHT};
    padding: 6px;
    font-weight: bold;
    font-size: {AppConfig.FONT_SIZE}pt;
}}

/* 垂直表头（行号）- 白色背景 */
QHeaderView::section:vertical {{
    background-color: {StyleConfig.SURFACE_COLOR};
    color: {StyleConfig.TEXT_PRIMARY};
    border: 1px solid {StyleConfig.BORDER_LIGHT};
    padding: 6px;
}}

/* 标签样式 */
QLabel {{
    color: {StyleConfig.TEXT_PRIMARY};
}}

/* 复选框样式 */
QCheckBox {{
    color: {StyleConfig.TEXT_PRIMARY};
}}

QCheckBox::indicator:checked {{
    background-color: {StyleConfig.PRIMARY_COLOR};
    border: 1px solid {StyleConfig.PRIMARY_COLOR};
}}

/* 选项卡样式 */
QTabWidget::pane {{
    border: 1px solid {StyleConfig.BORDER_COLOR};
    background-color: {StyleConfig.SURFACE_COLOR};
}}

QTabBar::tab {{
    background-color: {StyleConfig.ALTERNATE_BG};
    color: {StyleConfig.TEXT_PRIMARY};
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}}

QTabBar::tab:selected {{
    background-color: {StyleConfig.PRIMARY_COLOR};
    color: {StyleConfig.SURFACE_COLOR};
}}

QTabBar::tab:hover {{
    background-color: {StyleConfig.PRIMARY_HOVER};
    color: {StyleConfig.SURFACE_COLOR};
}}
"""


def apply_global_style(app):
    """应用全局样式"""
    app.setStyleSheet(get_unified_stylesheet())


# 导出常用配置
__all__ = [
    'AppConfig',
    'StyleConfig', 
    'ParameterConfig',
    'MLConfig',
    'get_unified_stylesheet',
    'apply_global_style'
]
