@echo off
chcp 65001 >nul
echo ========================================
echo Adams 仿真与优化工具集成平台 v2.0
echo ========================================
echo.

REM 获取脚本所在目录的父目录（项目根目录）
set "PROJECT_ROOT=%~dp0.."

REM 切换到项目根目录
cd /d "%PROJECT_ROOT%"

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python！
    echo.
    echo 解决方案：
    echo 1. 安装Python 3.7+
    echo 2. 确保Python添加到PATH环境变量
    echo 3. 重新打开命令提示符
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
python --version
echo.

REM 检查关键依赖
echo 正在检查关键依赖...
python -c "import PyQt5; print('✅ PyQt5: OK')" 2>nul
if errorlevel 1 (
    echo ⚠️  PyQt5未安装，正在尝试自动安装依赖...
    echo.
    python scripts\install_dependencies.py
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请手动运行: python scripts\install_dependencies.py
        pause
        exit /b 1
    )
)

REM 启动应用程序
echo.
echo 🚀 正在启动应用程序...
echo 注意：如果看到PyQt5弃用警告，这是正常的，不影响程序运行。
echo.

REM 优先使用 run_app.py，如果失败则使用 main.py
python run_app.py
if errorlevel 1 (
    echo.
    echo ⚠️  run_app.py 启动失败，尝试直接运行 main.py...
    python main.py
)

REM 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ========================================
    echo ❌ 程序异常退出，错误代码：%errorlevel%
    echo ========================================
    echo.
    echo 可能的解决方案：
    echo 1. 运行依赖检查: python scripts\install_dependencies.py
    echo 2. 检查错误信息并安装缺失的包
    echo 3. 确保所有依赖都已正确安装
    echo.
    echo 如需帮助，请查看 docs\ 目录下的文档
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)
