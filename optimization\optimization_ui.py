#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习 + GA 优化 GUI
与 batch_simulation_tool 的主界面风格保持一致。
"""

import os
import sys
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
    QPushButton, QFileDialog, QGroupBox, QSpinBox, QDoubleSpinBox, QTableWidget, QTableWidgetItem,
    QMessageBox, QComboBox, QFormLayout, QProgressDialog, QHeaderView, QSplitter,
    QFrame, QScrollArea, QSizePolicy, QSpacerItem
)

# 尝试导入共享样式，如果失败则使用本地样式
try:
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from shared_styles import apply_unified_style, UNIFIED_STYLESHEET
    SHARED_STYLES_AVAILABLE = True
except ImportError:
    SHARED_STYLES_AVAILABLE = False


# 代理模型中英文映射表
_MODEL_DISPLAY_TO_CODE = {
    "随机森林": "RandomForest",
    "极端随机森林": "ExtraTrees",
    "堆叠集成": "Stack",
}

# 参数中文+单位映射
_PARAM_DISPLAY = {
    "sun_stiff": "接触刚度（s-p） (N/mm)",
    "ring_stiff": "接触刚度（r-p） (N/mm)",
    "sun_damping": "接触阻尼（s-p） (N·s/mm)",
    "ring_damping": "接触阻尼（r-p） (N·s/mm)",
    "sun_exponent": "力指数（s-p）",  # 无单位
    "ring_exponent": "力指数（r-p）",  # 无单位
    "sun_dmax": "穿透深度（s-p） (mm)",
    "ring_dmax": "穿透深度（r-p） (mm)",
}

from ml_ga_optimization import optimize

class OptimizeWorker(QThread):
    progress = pyqtSignal(str)
    finished = pyqtSignal(dict)
    error = pyqtSignal(str)

    def __init__(self, xlsx_path, test_ratio, generations, pop_size, model_name):
        super().__init__()
        self.xlsx_path = xlsx_path
        self.test_ratio = test_ratio
        self.generations = generations
        self.pop_size = pop_size
        self.model_name = model_name

    def run(self):
        try:
            self.progress.emit("开始执行优化...")
            result = optimize(
                self.xlsx_path,
                test_ratio=self.test_ratio,
                generations=self.generations,
                pop_size=self.pop_size,
                model_name=self.model_name,
                progress_callback=lambda pct: self.progress.emit(f"{pct}%")
            )
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class OptimizationUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("模型修正")
        self.resize(1000, 650)
        self._init_ui()

        # 应用统一样式
        if SHARED_STYLES_AVAILABLE:
            apply_unified_style(QApplication.instance())
        else:
            QApplication.instance().setFont(QFont("Microsoft YaHei UI", 11))
            QApplication.instance().setStyleSheet(_UNIFIED_STYLE_QSS)

    def _init_ui(self):
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QHBoxLayout(central)
        
        # 创建左右分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # ----- 左侧参数设置区 -----
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)
        
        # ----- 文件选择 -----
        # 添加标题标签
        file_title = QLabel("仿真文件设置")
        file_title.setStyleSheet("color: #0078D4; font-weight: bold;")
        left_layout.addWidget(file_title)
        
        # 添加无标题的分组框
        file_group = self._create_group_box("")
        flayout = QHBoxLayout()
        flayout.addWidget(QLabel("仿真结果文件:"))
        self.file_edit = QLineEdit()
        self.file_edit.setPlaceholderText("请选择 error_summary.xlsx 文件")
        flayout.addWidget(self.file_edit, 1)
        browse_btn = QPushButton("浏览")
        browse_btn.clicked.connect(self.browse_file)
        flayout.addWidget(browse_btn)
        flayout.setContentsMargins(8, 4, 8, 4)
        flayout.setSpacing(6)
        file_group.setLayout(flayout)
        file_group.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)
        left_layout.addWidget(file_group)

        # ----- 优化算法配置 -----
        # 添加标题标签
        ga_title = QLabel("优化算法配置")
        ga_title.setStyleSheet("color: #0078D4; font-weight: bold;")
        left_layout.addWidget(ga_title)
        
        # 添加无标题的分组框
        ga_group = self._create_group_box("")
        glayout = QFormLayout()
        glayout.setContentsMargins(8, 10, 8, 10)  # 上下内边距增加到10px
        glayout.setSpacing(10)  # 组件间距增加到15px
        
        self.model_combo = QComboBox()
        self.model_combo.addItems(list(_MODEL_DISPLAY_TO_CODE.keys()))
        glayout.addRow("拟合模型:", self.model_combo)
        # 添加一个空行增加间距
        glayout.addItem(QSpacerItem(0, 5))
        
        self.test_ratio_spin = QDoubleSpinBox()
        self.test_ratio_spin.setRange(0.01, 0.99)  # 0-1范围，但避免极端值
        self.test_ratio_spin.setSingleStep(0.01)
        self.test_ratio_spin.setDecimals(2)  # 保留2位小数
        self.test_ratio_spin.setValue(0.30)
        self.test_ratio_spin.setToolTip("测试集比例，范围：0.01-0.99")
        self.test_ratio_spin.setKeyboardTracking(False)  # 只在编辑完成后触发信号
        self.test_ratio_spin.valueChanged.connect(self._validate_test_ratio)
        glayout.addRow("测试集比例:", self.test_ratio_spin)
        # 添加一个空行增加间距
        glayout.addItem(QSpacerItem(0, 5))
        
        self.gen_spin = QSpinBox()
        self.gen_spin.setRange(1, 10000)  # 正整数，最小值为1
        self.gen_spin.setValue(200)
        self.gen_spin.setToolTip("迭代次数，范围：1-10000")
        self.gen_spin.setKeyboardTracking(False)  # 只在编辑完成后触发信号
        self.gen_spin.valueChanged.connect(self._validate_generations)
        glayout.addRow("迭代次数:", self.gen_spin)
        # 添加一个空行增加间距
        glayout.addItem(QSpacerItem(0, 5))
        
        self.pop_spin = QSpinBox()
        self.pop_spin.setRange(1, 1000)  # 正整数，最小值为1
        self.pop_spin.setValue(50)
        self.pop_spin.setToolTip("种群规模，范围：1-1000")
        self.pop_spin.setKeyboardTracking(False)  # 只在编辑完成后触发信号
        self.pop_spin.valueChanged.connect(self._validate_pop_size)
        glayout.addRow("种群规模:", self.pop_spin)
        
        ga_group.setLayout(glayout)
        ga_group.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)
        left_layout.addWidget(ga_group)
        
        # ----- 操作按钮 -----
        btn_layout = QHBoxLayout()
        self.run_btn = QPushButton("开始优化")
        self.run_btn.clicked.connect(self.start_optimize)
        btn_layout.addStretch(1)
        btn_layout.addWidget(self.run_btn)
        left_layout.addLayout(btn_layout)

        # ----- 参数说明（左侧底部） -----
        info_text = (
            "<b>表格参数说明</b><br>"
            "s-p: 太阳轮与行星轮接触参数<br>"
            "r-p: 内齿圈与行星轮接触参数"
        )
        info_label = QLabel(info_text)
        info_label.setStyleSheet("font-size:10pt; color:#333333;")
        info_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        info_label.setTextFormat(Qt.RichText)
        left_layout.addWidget(info_label)
        
        # 添加弹性空间
        left_layout.addStretch(1)
        
        # ----- 右侧结果显示区 -----
        right_widget = QScrollArea()
        right_widget.setWidgetResizable(True)
        right_content = QWidget()
        right_layout = QVBoxLayout(right_content)
        right_layout.setContentsMargins(10, 10, 10, 10)
        
        # ----- 优化结果区域 -----
        # 添加标题标签
        result_title = QLabel("参数优化结果")
        result_title.setStyleSheet("color: #0078D4; font-weight: bold;")
        right_layout.addWidget(result_title)
        
        # 添加无标题的分组框
        result_group = self._create_group_box("")
        result_layout = QVBoxLayout()
        
        # 结果表格
        self.result_table = QTableWidget(0, 2)
        self.result_table.setHorizontalHeaderLabels(["仿真参数", "最优值"])
        self.result_table.horizontalHeader().setDefaultAlignment(Qt.AlignCenter)
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.result_table.verticalHeader().setVisible(False)
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setShowGrid(False)
        self.result_table.setStyleSheet(
            "QHeaderView::section {background-color:#0078D4; color:#FFFFFF; font-weight:bold;}\n"
            "QTableWidget {alternate-background-color:#FAF9F8;}"
        )
        result_layout.addWidget(self.result_table)
        
        # 移除右侧指标显示（已转移到左侧）
        
        result_group.setLayout(result_layout)
        right_layout.addWidget(result_group)
        
        # 将右侧内容添加到滚动区域
        right_widget.setWidget(right_content)
        
        # 添加左右两侧到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([300, 700])  # 设置初始分割比例

    def _create_group_box(self, title=""):
        """创建带蓝色标题的分组框，但不使用 QGroupBox 的标题"""
        # 创建无标题的 QGroupBox
        group = QGroupBox()
        group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #0078D4;
                border-radius: 4px;
                margin-top: 0px;
            }
        """)
        return group

    # ---------------------- 槽函数 ----------------------
    def browse_file(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择 error_summary.xlsx", os.getcwd(), "Excel Files (*.xlsx)")
        if path:
            self.file_edit.setText(path)

    def _validate_test_ratio(self, value):
        """验证测试集比例输入"""
        if not (0.01 <= value <= 0.99):
            self.test_ratio_spin.setStyleSheet("QDoubleSpinBox { border: 2px solid red; }")
            self.test_ratio_spin.setToolTip(f"测试集比例必须在0.01-0.99之间！当前值：{value}")
        else:
            self.test_ratio_spin.setStyleSheet("")
            self.test_ratio_spin.setToolTip("测试集比例，范围：0.01-0.99")

    def _validate_generations(self, value):
        """验证迭代次数输入"""
        if not (1 <= value <= 10000):
            self.gen_spin.setStyleSheet("QSpinBox { border: 2px solid red; }")
            self.gen_spin.setToolTip(f"迭代次数必须在1-10000之间！当前值：{value}")
        else:
            self.gen_spin.setStyleSheet("")
            self.gen_spin.setToolTip("迭代次数，范围：1-10000")

    def _validate_pop_size(self, value):
        """验证种群规模输入"""
        if not (1 <= value <= 1000):
            self.pop_spin.setStyleSheet("QSpinBox { border: 2px solid red; }")
            self.pop_spin.setToolTip(f"种群规模必须在1-1000之间！当前值：{value}")
        else:
            self.pop_spin.setStyleSheet("")
            self.pop_spin.setToolTip("种群规模，范围：1-1000")

    def start_optimize(self):
        # 输入验证
        xlsx_path = self.file_edit.text().strip()
        if not xlsx_path or not os.path.exists(xlsx_path):
            QMessageBox.warning(self, "提示", "请先选择 error_summary.xlsx 文件！")
            return

        # 验证参数范围
        test_ratio = self.test_ratio_spin.value()
        generations = self.gen_spin.value()
        pop_size = self.pop_spin.value()

        # 测试集比例验证
        if not (0.01 <= test_ratio <= 0.99):
            QMessageBox.warning(self, "参数错误", f"测试集比例必须在0.01-0.99之间！\n当前值：{test_ratio}")
            return

        # 迭代次数验证
        if not (1 <= generations <= 10000):
            QMessageBox.warning(self, "参数错误", f"迭代次数必须在1-10000之间的正整数！\n当前值：{generations}")
            return

        # 种群规模验证
        if not (1 <= pop_size <= 1000):
            QMessageBox.warning(self, "参数错误", f"种群规模必须在1-1000之间的正整数！\n当前值：{pop_size}")
            return

        self.run_btn.setEnabled(False)

        self.worker = OptimizeWorker(
            xlsx_path,
            test_ratio=test_ratio,
            generations=generations,
            pop_size=pop_size,
            model_name=_MODEL_DISPLAY_TO_CODE.get(self.model_combo.currentText(), "RandomForest"),
        )
        self.progress_dlg = QProgressDialog("优化中...", "取消", 0, 100, self)
        self.progress_dlg.canceled.connect(self.cancel_optimize)
        self.progress_dlg.setWindowModality(Qt.WindowModal)

        self.worker.progress.connect(self._update_progress)
        self.worker.finished.connect(self.on_opt_finished)
        self.worker.error.connect(self.on_opt_error)
        self.worker.start()

    def _update_progress(self, msg):
        try:
            pct = int(msg.strip('%'))
            if hasattr(self, 'progress_dlg'):
                self.progress_dlg.setValue(pct)
        except Exception:
            pass
        self.statusBar().showMessage(msg)

    def cancel_optimize(self):
        if hasattr(self, 'worker') and self.worker.isRunning():
            self.worker.requestInterruption()
        self.statusBar().showMessage("已请求取消…")

    def on_opt_finished(self, result):
        self.run_btn.setEnabled(True)
        if hasattr(self, 'progress_dlg'):
            self.progress_dlg.reset()
        self.latest_result = result
        
        # 更新结果表格
        self.result_table.setRowCount(0)  # 清空表格
        param_names = result['param_names']
        best_vals = result['best_solution']
        self.result_table.setRowCount(len(param_names))
        for i, (n, v) in enumerate(zip(param_names, best_vals)):
            display_name = _PARAM_DISPLAY.get(n, n)
            item_name = QTableWidgetItem(display_name)
            item_val = QTableWidgetItem(f"{v:.6g}")
            item_name.setTextAlignment(Qt.AlignCenter)
            item_val.setTextAlignment(Qt.AlignCenter)
            self.result_table.setItem(i, 0, item_name)
            self.result_table.setItem(i, 1, item_val)

        # 状态栏显示
        self.statusBar().showMessage(f"优化完成 | RMSE: {result['rmse']:.4g} | R²: {result['r2']:.4g}")

    def on_opt_error(self, msg):
        self.run_btn.setEnabled(True)
        if hasattr(self, 'progress_dlg'):
            self.progress_dlg.reset()
        QMessageBox.critical(self, "错误", f"优化过程中发生错误:\n{msg}")
        self.statusBar().showMessage("发生错误")


# ---------------- 统一的全局样式 ----------------

_UNIFIED_STYLE_QSS = """
/* 主窗口背景 */
QMainWindow {
    background-color: #F3F2F1;
}

/* 按钮样式 - 与图片一致的深蓝色主题 */
QPushButton {
    background-color: #0078D4;
    color: #FFFFFF;
    border: 1px solid #0078D4;
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #106EBE;
    border-color: #106EBE;
}

QPushButton:pressed {
    background-color: #005A9E;
    border-color: #005A9E;
}

QPushButton:disabled {
    background-color: #6C757D;
    border-color: #6C757D;
    color: #E9ECEF;
}

/* 分组框样式 - 与图片一致的深蓝色主题 */
QGroupBox {
    border: 1px solid #EDEBE9;
    border-radius: 4px;
    margin-top: 15px;
    padding-top: 20px;
    font-weight: bold;
}

QGroupBox:title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    color: #0078D4;
    font-weight: bold;
}

/* 输入控件样式 */
QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
    border: 1px solid #E1DFDD;
    border-radius: 4px;
    padding: 4px;
    background-color: #FFFFFF;
    min-height: 16px;
}

QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border-color: #0078D4;
    outline: none;
}

/* 表格样式 */
QTableWidget {
    border: 1px solid #E1DFDD;
    background-color: #FFFFFF;
    alternate-background-color: #FAF9F8;
    gridline-color: #EDEBE9;
}

QHeaderView::section {
    background-color: #0078D4;
    color: #FFFFFF;
    border: 1px solid #E1DFDD;
    padding: 6px;
    font-weight: bold;
}

/* 标签样式 */
QLabel {
    color: #323130;
}

/* 复选框样式 */
QCheckBox {
    color: #323130;
}

QCheckBox::indicator:checked {
    background-color: #0078D4;
    border: 1px solid #0078D4;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #F8F9FA;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #CED4DA;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #0D6EFD;
}

/* 进度条样式 */
QProgressBar {
    border: 1px solid #CED4DA;
    border-radius: 4px;
    text-align: center;
    background-color: #F8F9FA;
}

QProgressBar::chunk {
    background-color: #0D6EFD;
    border-radius: 3px;
}
"""


if __name__ == '__main__':
    app = QApplication.instance()
    new_app_created = False
    if app is None:
        app = QApplication(sys.argv)
        new_app_created = True

    ui = OptimizationUI()
    ui.show()

    if new_app_created:
        sys.exit(app.exec_()) 