#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参数采样功能 - 验证所有参数都参与采样
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_parameter_sampling():
    """测试参数采样功能"""
    print("=" * 60)
    print("测试参数采样功能 - 验证所有参数都参与采样")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from utils.data_processor import generate_parameter_combinations
        from config import ParameterConfig
        
        # 模拟参数范围配置（所有参数都启用）
        param_ranges = {
            "sun_stiff": {"min": 360000, "max": 400000, "enabled": True},
            "ring_stiff": {"min": 740000, "max": 780000, "enabled": True},
            "sun_damping": {"min": 0, "max": 50, "enabled": True},
            "ring_damping": {"min": 0, "max": 80, "enabled": True},
            "sun_exponent": {"min": 1.5, "max": 3.0, "enabled": True},
            "ring_exponent": {"min": 1.5, "max": 3.0, "enabled": True},
            "sun_dmax": {"min": 0.05, "max": 0.2, "enabled": True},
            "ring_dmax": {"min": 0.05, "max": 0.2, "enabled": True}
        }
        
        # 模拟工况数据
        conditions = [
            {"speed": 1500, "torque": 100, "test_rms": 0.85},
            {"speed": 2000, "torque": 150, "test_rms": 0.90},
            {"speed": 2500, "torque": 200, "test_rms": 0.95}
        ]
        
        # 生成参数组合
        n_samples = 10
        print(f"🔄 生成 {n_samples} 个参数样本...")
        df = generate_parameter_combinations(param_ranges, conditions, n_samples)
        
        print(f"✅ 成功生成参数组合")
        print(f"📊 数据形状: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 验证所有参数都包含在结果中
        expected_params = set(param_ranges.keys())
        actual_params = set(df.columns) - {"speed", "torque", "test_rms"}
        
        print(f"\n🔍 参数验证:")
        print(f"期望参数数量: {len(expected_params)}")
        print(f"实际参数数量: {len(actual_params)}")
        
        if expected_params == actual_params:
            print("✅ 所有参数都参与了采样")
        else:
            missing = expected_params - actual_params
            extra = actual_params - expected_params
            if missing:
                print(f"❌ 缺少参数: {missing}")
            if extra:
                print(f"⚠️  额外参数: {extra}")
        
        # 显示前几行数据
        print(f"\n📋 前5行数据预览:")
        print(df.head().to_string(index=False))
        
        # 验证参数范围
        print(f"\n🔍 参数范围验证:")
        for param_name in expected_params:
            if param_name in df.columns:
                min_val = df[param_name].min()
                max_val = df[param_name].max()
                expected_min = param_ranges[param_name]["min"]
                expected_max = param_ranges[param_name]["max"]
                
                in_range = (min_val >= expected_min) and (max_val <= expected_max)
                status = "✅" if in_range else "❌"
                print(f"{status} {param_name}: [{min_val:.3f}, {max_val:.3f}] (期望: [{expected_min}, {expected_max}])")
        
        print(f"\n🎉 参数采样功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_parameter_sampling()
    sys.exit(0 if success else 1)
