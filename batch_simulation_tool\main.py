#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Adams 批量仿真工具主程序
启动批量仿真界面
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from batch_simulation_ui import BatchSimulationUI
from PyQt5.QtWidgets import QApplication

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("Adams 批量仿真工具")
    app.setApplicationVersion("1.0.0")
    
    # 创建并显示主窗口
    window = BatchSimulationUI()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 