import os
import pandas as pd
import numpy as np

# 从本地工具模块导入所需函数
from adams_utils import send_adams_command, process_simulation_results

def run_adams_batch_simulation(save_base_path, sim_time, sim_steps, param_combos, progress_callback=None, stop_flag_check=None):
    """
    执行Adams批量仿真。
    参数:
    save_base_path (str): 仿真结果的保存基础路径。
    sim_time (float): 仿真时间。
    sim_steps (int): 仿真步数。
    param_combos (list): 参数组合列表，每个元素是一个字典。(此参数目前未使用，因为直接从文件读取)
    progress_callback (callable, optional): 用于更新进度的回调函数，接收 (progress_percent, status_text) 参数。
    stop_flag_check (callable, optional): 用于检查是否需要停止的函数，返回True表示停止。
    """
    # 直接读取batch_params.xlsx
    param_path = os.path.join(save_base_path, "batch_params.xlsx")
    if not os.path.exists(param_path):
        if progress_callback: progress_callback(0, "未找到参数文件 batch_params.xlsx")
        raise FileNotFoundError("未找到参数文件 batch_params.xlsx")
    
    df = pd.read_excel(param_path)
    total_simulations = len(df)
    
    contact_names = [
        ".MODEL_6_6.CONTACT_1", ".MODEL_6_6.CONTACT_2", ".MODEL_6_6.CONTACT_3",  # 太阳轮-行星轮
        ".MODEL_6_6.CONTACT_4", ".MODEL_6_6.CONTACT_5", ".MODEL_6_6.CONTACT_6"  # 行星轮-内齿圈
    ]

    # --------------------------- 恢复功能 ---------------------------
    # 判断哪些行已完成 (仿真RMS值为数值且不是 NaN)
    def _is_completed(val):
        """返回 True 表示该行仿真已完成且 RMS 有效。"""
        try:
            if pd.isna(val):
                return False
            # 尝试将其转换为浮点数
            _ = float(val)
            return True
        except (ValueError, TypeError):
            return False

    completed_mask = df['仿真RMS值'].apply(_is_completed) if '仿真RMS值' in df.columns else pd.Series([False]*len(df))
    if completed_mask.all():
        if progress_callback:
            progress_callback(100, "所有仿真已完成，无需重新运行")
        return

    # 累计完成计数（包括已完成 + 跑完的）用于进度条
    done_so_far = completed_mask.sum()

    for idx, row in df.iterrows():
        sim_index = idx + 1
        if stop_flag_check and stop_flag_check():
            if progress_callback: progress_callback(int(idx / total_simulations * 100), "批量仿真已暂停")
            return

        # 若该行已完成且无需重新计算，直接更新进度并跳过
        if completed_mask.iloc[idx]:
            if progress_callback:
                progress_callback(int(done_so_far / total_simulations * 100), f"跳过第 {sim_index}/{total_simulations} 组 (已完成)")
            continue

        # 兼容各种可能的参数名
        params = {
            'sun_stiff': row.get('sun_stiff', row.get('s-p刚度(N/mm)', 0)),
            'ring_stiff': row.get('ring_stiff', row.get('p-r刚度(N/mm)', 0)),
            'sun_damping': row.get('sun_damping', row.get('s-p阻尼(N)', 0)),
            'ring_damping': row.get('ring_damping', row.get('p-r阻尼(N)', 0)),
            'sun_exponent': row.get('sun_exponent', row.get('s-p力指数', 0)),
            'ring_exponent': row.get('ring_exponent', row.get('p-r力指数', 0)),
            'sun_dmax': row.get('sun_dmax', row.get('s-p穿透深度(mm)', 0)),
            'ring_dmax': row.get('ring_dmax', row.get('p-r穿透深度(mm)', 0)),
        }
        
        # 兼容各种可能的工况名并确保类型安全
        speed = pd.to_numeric(row.get('工况_转速(rpm)', row.get('转速(rpm)')), errors='coerce')
        torque = pd.to_numeric(row.get('工况_负载扭矩(N·m)', row.get('负载扭矩(N·m)')), errors='coerce')
        
        # 如果转换失败，pd.to_numeric返回NaN，用0填充
        speed = speed if pd.notna(speed) else 0
        torque = torque if pd.notna(torque) else 0

        # 单位换算
        deg_per_sec = speed * 6
        torque_nmm = torque * 1000
        drive_time = 1.0 # 假设驱动加载时间为1s

        # 构造发送给Adams的命令列表
        commands = []
        
        # 1. 设置接触参数
        # 太阳轮-行星轮 (前3个)
        for j in range(3):
            cmd = (f"cmd contact modify contact_name={contact_names[j]} "
                   f"stiffness={params['sun_stiff']} damping={params['sun_damping']} "
                   f"exponent={params['sun_exponent']} dmax={params['sun_dmax']} "
                   f"coulomb_friction=on mu_static=0.08 mu_dynamic=0.05 "
                   f"stiction_transition_velocity=0.1 friction_transition_velocity=10.0")
            commands.append(cmd)
        
        # 行星轮-内齿圈 (后3个)
        for j in range(3, 6):
            cmd = (f"cmd contact modify contact_name={contact_names[j]} "
                   f"stiffness={params['ring_stiff']} damping={params['ring_damping']} "
                   f"exponent={params['ring_exponent']} dmax={params['ring_dmax']} "
                   f"coulomb_friction=on mu_static=0.08 mu_dynamic=0.05 "
                   f"stiction_transition_velocity=0.1 friction_transition_velocity=10.0")
            commands.append(cmd)

        # 2. 设置转速和扭矩
        commands.append(
            f'cmd constraint modify motion motion_name=.MODEL_6_6.MOTION_1 joint_name=.MODEL_6_6.JOINT_4 type_of_freedom=rotational function="step(time,0,0d,{drive_time},{deg_per_sec}d)" time_derivative="velocity"'
        )
        commands.append(
            f'cmd force modify direct single_component_force single_component_force=.MODEL_6_6.SFORCE_1 function="{torque_nmm}" action_only="on"'
        )
        
        # 3. 仿真命令
        commands.extend([
            f"cmd simulation single trans type=auto_select initial_static=no end_time={sim_time} number_of_steps={sim_steps}",
            "cmd simulation single reset",
            "cmd simulation single set update=\"none\""
        ])

        # 执行命令
        for cmd in commands:
            if not send_adams_command(cmd):
                error_msg = f"执行命令失败：{cmd}"
                if progress_callback: progress_callback(int(sim_index / total_simulations * 100), error_msg)
                raise Exception(error_msg)

        # 4. 导出结果
        export_dir = os.path.normpath(os.path.join(save_base_path, f"sim_{sim_index:03d}"))
        os.makedirs(export_dir, exist_ok=True)
        html_file = os.path.join(export_dir, f"sim_results_{sim_index:03d}.htm")
        xlsx_file = os.path.join(export_dir, f"sim_results_{sim_index:03d}.xlsx")

        adams_export_commands = [
            "cmd interface plot window open",
            "cmd undo begin suppress=yes",
            "cmd xy_plot template clear plot=.plot_1",
            "cmd xy_plot template modify plot=.plot_1 auto_title=yes auto_subtitle=no auto_date=yes auto_analysis_name=yes table=no",
            f"cmd xy_plot curve create curve=.plot_1.curve_1 create_page=no calculate_axis_limits=no dexpression=\"MEASURE(.MODEL_6_6.ring_flex.original_rigid_cm, 0, 0, Translational_Acceleration, Z_component)\" legend=\".original_rigid_cm.Translational_Acceleration.Z\" d_units=\"acceleration\" run=.MODEL_6_6.Last_Run auto_axis=UNITS",
            "cmd xy_plot template auto_zoom plot_name=.plot_1",
            "cmd xy_plot template calculate_axis_limits plot_name=.plot_1",
            "cmd interface plot window update_toolbar",
            "cmd undo end",
            f"cmd file table write file_name=\"{html_file}\" plot_name=.plot_1 format=html"
        ]

        for cmd in adams_export_commands:
            if not send_adams_command(cmd):
                error_msg = f"执行导出命令失败: {cmd}"
                if progress_callback: progress_callback(int(sim_index / total_simulations * 100), error_msg)
                raise Exception(error_msg)
        
        # 5. 处理结果并更新参数文件
        try:
            rms_value = process_simulation_results(html_file, xlsx_file)
            df.loc[idx, '仿真RMS值'] = rms_value

            # 立即写回 Excel，使用户实时看到结果
            try:
                df.to_excel(param_path, index=False)
            except PermissionError:
                print(
                    "无法写入 batch_params.xlsx，可能文件正在被占用（例如已在Excel中打开）。\n"
                    "请关闭文件后重试，或在仿真完成后查看结果。")
            except Exception as write_e:
                print(f"实时写入 batch_params.xlsx 失败: {write_e}")
        except Exception as e:
            error_msg = f"处理第 {sim_index} 组仿真结果时出错: {e}"
            print(error_msg)
            df.loc[idx, '仿真RMS值'] = 'Error'
            if progress_callback:
                progress_callback(int(sim_index / total_simulations * 100), error_msg)
            # 关键：遇到严重错误，立即终止整个批量仿真
            raise Exception(error_msg)

        # 6. 更新进度
        done_so_far += 1
        if progress_callback:
            progress_callback(int(done_so_far / total_simulations * 100), f"完成第 {sim_index}/{total_simulations} 组仿真")

    # 7. 所有仿真完成后，将带有RMS结果的DataFrame写回Excel文件
    try:
        df.to_excel(param_path, index=False)
        print(f"批量仿真完成，RMS结果已写回 {param_path}")
    except Exception as e:
        print(f"写回RMS结果到Excel失败: {e}")
        # 可以在这里决定是否抛出异常

    if progress_callback: progress_callback(100, "批量仿真完成")


def calculate_relative_error_summary(param_file_path, summary_file_path, all_param_display_names=None, max_total_error=None):
    """
    在所有仿真完成后，计算每组参数的总相对误差。

    参数:
    param_file_path (str): 包含所有仿真结果的 "batch_params.xlsx" 文件路径。
    summary_file_path (str): 保存误差汇总结果的文件路径。
    all_param_display_names (list): 从UI表格获取的所有参数的显示名称列表。
    max_total_error (float): 最大允许的总相对误差。
    """
    df = pd.read_excel(param_file_path)

    # 将参数的显示名称映射到代码中的名称
    param_names_map = {
        "s-p刚度(N/mm)": "sun_stiff", "p-r刚度(N/mm)": "ring_stiff",
        "s-p阻尼(N)": "sun_damping", "p-r阻尼(N)": "ring_damping",
        "s-p力指数": "sun_exponent", "p-r力指数": "ring_exponent",
        "s-p穿透深度(mm)": "sun_dmax", "p-r穿透深度(mm)": "ring_dmax"
    }

    # 如果未提供显示名列表，则基于文件列推断
    if not all_param_display_names:
        # 反向映射: code -> display
        reverse_map = {v: k for k, v in param_names_map.items()}
        all_param_display_names = [reverse_map[col] for col in df.columns if col in reverse_map]

    # 获取实际参与了参数生成的列名 (codes)
    param_code_names = [param_names_map[name] for name in all_param_display_names if name in param_names_map]
    
    # 确保所有需要的列都存在
    required_cols = param_code_names + ['试验RMS值', '仿真RMS值']
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"输入文件 '{os.path.basename(param_file_path)}' 中缺少必需的列: {col}")

    # 将仿真RMS列转换为数值，错误的值（如'Error'）将变为NaN
    df['仿真RMS值'] = pd.to_numeric(df['仿真RMS值'], errors='coerce')

    # 仅保留已经完成仿真的记录
    df = df.dropna(subset=['仿真RMS值'])
    if df.empty:
        raise ValueError("尚未有任何仿真结果可用于误差计算！")

    # 计算相对误差
    # 避免除以零的情况
    df['相对误差'] = np.where(
        df['试验RMS值'] == 0,
        np.inf, # 如果试验值为0，相对误差设为无穷大
        abs(df['仿真RMS值'] - df['试验RMS值']) / df['试验RMS值']
    )

    # 按参数组合进行分组，并计算总相对误差
    summary = df.groupby(param_code_names).agg(
        总相对误差=('相对误差', 'sum')
    ).reset_index()

    # 统计信息
    total_groups = len(summary)

    if max_total_error is not None:
        good_mask = summary['总相对误差'] <= max_total_error
        kept_groups = int(good_mask.sum())
        filtered_groups = total_groups - kept_groups
    else:
        kept_groups = total_groups
        filtered_groups = 0

    # 始终保存完整 summary，便于后续范围收缩使用完整好/坏数据
    summary.to_excel(summary_file_path, index=False)

    print(f"误差汇总结果已保存至 {summary_file_path}")

    return total_groups, kept_groups, filtered_groups 