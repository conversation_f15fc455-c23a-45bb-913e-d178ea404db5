#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量仿真核心逻辑模块

提供Adams批量仿真的核心功能。
重构自原来的 batch_simulation_logic.py 文件。

作者: Adams Simulation Team
版本: 2.0.0
"""

import os
import pandas as pd
import numpy as np
from typing import Callable, Optional, List, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal

# 导入工具模块
from utils.adams_interface import send_adams_command, process_simulation_results
from utils.file_manager import load_from_excel, save_to_excel
from utils.data_processor import calculate_relative_errors, calculate_total_relative_error
from config import AppConfig


class BatchSimulator(QObject):
    """批量仿真器类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度更新信号 (百分比, 状态文本)
    simulation_finished = pyqtSignal()       # 仿真完成信号
    simulation_error = pyqtSignal(str)       # 仿真错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_running = False
        self.should_stop = False
        
        # Adams模型中的接触名称
        self.contact_names = [
            ".MODEL_6_6.CONTACT_1", ".MODEL_6_6.CONTACT_2", ".MODEL_6_6.CONTACT_3",  # 太阳轮-行星轮
            ".MODEL_6_6.CONTACT_4", ".MODEL_6_6.CONTACT_5", ".MODEL_6_6.CONTACT_6"   # 行星轮-内齿圈
        ]
    
    def start_simulation(self, save_base_path: str, 
                        sim_time: float = None, 
                        sim_steps: int = None):
        """
        启动批量仿真。
        
        Args:
            save_base_path (str): 仿真结果保存路径
            sim_time (float): 仿真时间，默认使用配置值
            sim_steps (int): 仿真步数，默认使用配置值
        """
        if self.is_running:
            self.simulation_error.emit("仿真已在运行中")
            return
        
        # 使用默认配置
        if sim_time is None:
            sim_time = AppConfig.DEFAULT_SIM_TIME
        if sim_steps is None:
            sim_steps = AppConfig.DEFAULT_SIM_STEPS
        
        self.is_running = True
        self.should_stop = False
        
        try:
            self._run_batch_simulation(save_base_path, sim_time, sim_steps)
        except Exception as e:
            self.simulation_error.emit(str(e))
        finally:
            self.is_running = False
    
    def stop_simulation(self):
        """停止仿真"""
        self.should_stop = True
    
    def _run_batch_simulation(self, save_base_path: str, sim_time: float, sim_steps: int):
        """执行批量仿真的核心逻辑"""
        # 读取参数文件
        param_path = os.path.join(save_base_path, AppConfig.BATCH_PARAMS_FILE)
        if not os.path.exists(param_path):
            raise FileNotFoundError(f"未找到参数文件: {AppConfig.BATCH_PARAMS_FILE}")
        
        df = load_from_excel(param_path)
        if df is None:
            raise Exception("无法读取参数文件")
        
        total_simulations = len(df)
        self.progress_updated.emit(0, f"开始批量仿真，共 {total_simulations} 组参数")
        
        # 检查已完成的仿真
        completed_mask = self._get_completed_mask(df)
        done_so_far = completed_mask.sum()
        
        if completed_mask.all():
            self.progress_updated.emit(100, "所有仿真已完成")
            self.simulation_finished.emit()
            return
        
        # 逐个执行仿真
        for idx, row in df.iterrows():
            if self.should_stop:
                self.progress_updated.emit(
                    int(done_so_far / total_simulations * 100), 
                    "仿真已暂停"
                )
                return
            
            sim_index = idx + 1
            
            # 跳过已完成的仿真
            if completed_mask.iloc[idx]:
                self.progress_updated.emit(
                    int(done_so_far / total_simulations * 100),
                    f"跳过第 {sim_index}/{total_simulations} 组 (已完成)"
                )
                continue
            
            # 执行单次仿真
            try:
                self._run_single_simulation(row, sim_index, sim_time, sim_steps, save_base_path)
                done_so_far += 1
                
                # 更新DataFrame并保存
                rms_value = self._get_simulation_result(save_base_path, sim_index)
                df.loc[idx, '仿真RMS值'] = rms_value
                
                # 实时保存结果
                save_to_excel(df, param_path, backup_existing=False)
                
                self.progress_updated.emit(
                    int(done_so_far / total_simulations * 100),
                    f"完成第 {sim_index}/{total_simulations} 组仿真"
                )
                
            except Exception as e:
                error_msg = f"第 {sim_index} 组仿真失败: {e}"
                df.loc[idx, '仿真RMS值'] = 'Error'
                save_to_excel(df, param_path, backup_existing=False)
                raise Exception(error_msg)
        
        # 保存最终结果
        save_to_excel(df, param_path, backup_existing=False)
        self.progress_updated.emit(100, "批量仿真完成")
        self.simulation_finished.emit()
    
    def _get_completed_mask(self, df: pd.DataFrame) -> pd.Series:
        """获取已完成仿真的掩码"""
        def is_completed(val):
            try:
                if pd.isna(val):
                    return False
                float(val)  # 尝试转换为浮点数
                return True
            except (ValueError, TypeError):
                return False
        
        if '仿真RMS值' in df.columns:
            return df['仿真RMS值'].apply(is_completed)
        else:
            return pd.Series([False] * len(df))
    
    def _run_single_simulation(self, row: pd.Series, sim_index: int, 
                              sim_time: float, sim_steps: int, save_base_path: str):
        """执行单次仿真"""
        # 提取参数
        params = self._extract_parameters(row)
        speed, torque = self._extract_conditions(row)
        
        # 生成Adams命令
        commands = self._generate_adams_commands(params, speed, torque, sim_time, sim_steps)
        
        # 执行仿真命令
        for cmd in commands:
            if not send_adams_command(cmd):
                raise Exception(f"执行命令失败: {cmd}")
        
        # 导出结果
        self._export_simulation_results(save_base_path, sim_index)
    
    def _extract_parameters(self, row: pd.Series) -> Dict[str, float]:
        """从行数据中提取参数"""
        return {
            'sun_stiff': row.get('sun_stiff', row.get('s-p刚度(N/mm)', 0)),
            'ring_stiff': row.get('ring_stiff', row.get('p-r刚度(N/mm)', 0)),
            'sun_damping': row.get('sun_damping', row.get('s-p阻尼(N)', 0)),
            'ring_damping': row.get('ring_damping', row.get('p-r阻尼(N)', 0)),
            'sun_exponent': row.get('sun_exponent', row.get('s-p力指数', 0)),
            'ring_exponent': row.get('ring_exponent', row.get('p-r力指数', 0)),
            'sun_dmax': row.get('sun_dmax', row.get('s-p穿透深度(mm)', 0)),
            'ring_dmax': row.get('ring_dmax', row.get('p-r穿透深度(mm)', 0)),
        }
    
    def _extract_conditions(self, row: pd.Series) -> tuple:
        """从行数据中提取工况条件"""
        speed = pd.to_numeric(
            row.get('工况_转速(rpm)', row.get('转速(rpm)', 0)), 
            errors='coerce'
        )
        torque = pd.to_numeric(
            row.get('工况_负载扭矩(N·m)', row.get('负载扭矩(N·m)', 0)), 
            errors='coerce'
        )
        
        # 处理NaN值
        speed = speed if pd.notna(speed) else 0
        torque = torque if pd.notna(torque) else 0
        
        return speed, torque
    
    def _generate_adams_commands(self, params: Dict[str, float], 
                                speed: float, torque: float,
                                sim_time: float, sim_steps: int) -> List[str]:
        """生成Adams命令列表"""
        commands = []
        
        # 1. 设置接触参数
        # 太阳轮-行星轮 (前3个)
        for j in range(3):
            cmd = (f"cmd contact modify contact_name={self.contact_names[j]} "
                   f"stiffness={params['sun_stiff']} damping={params['sun_damping']} "
                   f"exponent={params['sun_exponent']} dmax={params['sun_dmax']} "
                   f"coulomb_friction=on mu_static=0.08 mu_dynamic=0.05 "
                   f"stiction_transition_velocity=0.1 friction_transition_velocity=10.0")
            commands.append(cmd)
        
        # 行星轮-内齿圈 (后3个)
        for j in range(3, 6):
            cmd = (f"cmd contact modify contact_name={self.contact_names[j]} "
                   f"stiffness={params['ring_stiff']} damping={params['ring_damping']} "
                   f"exponent={params['ring_exponent']} dmax={params['ring_dmax']} "
                   f"coulomb_friction=on mu_static=0.08 mu_dynamic=0.05 "
                   f"stiction_transition_velocity=0.1 friction_transition_velocity=10.0")
            commands.append(cmd)
        
        # 2. 设置转速和扭矩
        deg_per_sec = speed * 6
        torque_nmm = torque * 1000
        drive_time = 1.0
        
        commands.extend([
            f'cmd constraint modify motion motion_name=.MODEL_6_6.MOTION_1 joint_name=.MODEL_6_6.JOINT_4 type_of_freedom=rotational function="step(time,0,0d,{drive_time},{deg_per_sec}d)" time_derivative="velocity"',
            f'cmd force modify direct single_component_force single_component_force=.MODEL_6_6.SFORCE_1 function="{torque_nmm}" action_only="on"'
        ])
        
        # 3. 仿真命令
        commands.extend([
            f"cmd simulation single trans type=auto_select initial_static=no end_time={sim_time} number_of_steps={sim_steps}",
            "cmd simulation single reset",
            "cmd simulation single set update=\"none\""
        ])
        
        return commands
    
    def _export_simulation_results(self, save_base_path: str, sim_index: int):
        """导出仿真结果"""
        export_dir = os.path.join(save_base_path, f"sim_{sim_index:03d}")
        os.makedirs(export_dir, exist_ok=True)
        html_file = os.path.join(export_dir, f"sim_results_{sim_index:03d}.htm")
        
        export_commands = [
            "cmd interface plot window open",
            "cmd undo begin suppress=yes",
            "cmd xy_plot template clear plot=.plot_1",
            "cmd xy_plot template modify plot=.plot_1 auto_title=yes auto_subtitle=no auto_date=yes auto_analysis_name=yes table=no",
            f"cmd xy_plot curve create curve=.plot_1.curve_1 create_page=no calculate_axis_limits=no dexpression=\"MEASURE(.MODEL_6_6.ring_flex.original_rigid_cm, 0, 0, Translational_Acceleration, Z_component)\" legend=\".original_rigid_cm.Translational_Acceleration.Z\" d_units=\"acceleration\" run=.MODEL_6_6.Last_Run auto_axis=UNITS",
            "cmd xy_plot template auto_zoom plot_name=.plot_1",
            "cmd xy_plot template calculate_axis_limits plot_name=.plot_1",
            "cmd interface plot window update_toolbar",
            "cmd undo end",
            f"cmd file table write file_name=\"{html_file}\" plot_name=.plot_1 format=html"
        ]
        
        for cmd in export_commands:
            if not send_adams_command(cmd):
                raise Exception(f"导出命令执行失败: {cmd}")
    
    def _get_simulation_result(self, save_base_path: str, sim_index: int) -> float:
        """获取仿真结果RMS值"""
        export_dir = os.path.join(save_base_path, f"sim_{sim_index:03d}")
        html_file = os.path.join(export_dir, f"sim_results_{sim_index:03d}.htm")
        xlsx_file = os.path.join(export_dir, f"sim_results_{sim_index:03d}.xlsx")
        
        return process_simulation_results(html_file, xlsx_file)


def run_batch_simulation(save_base_path: str,
                        sim_time: float = None,
                        sim_steps: int = None,
                        progress_callback: Optional[Callable] = None,
                        stop_flag_check: Optional[Callable] = None) -> None:
    """
    运行批量仿真的便捷函数。
    
    Args:
        save_base_path (str): 仿真结果保存路径
        sim_time (float): 仿真时间
        sim_steps (int): 仿真步数
        progress_callback (callable): 进度回调函数
        stop_flag_check (callable): 停止检查函数
    """
    simulator = BatchSimulator()
    
    # 连接回调函数
    if progress_callback:
        simulator.progress_updated.connect(
            lambda progress, status: progress_callback(progress, status)
        )
    
    # 设置停止检查
    if stop_flag_check:
        simulator.should_stop = stop_flag_check()
    
    simulator.start_simulation(save_base_path, sim_time, sim_steps)


def calculate_error_summary(param_file_path: str, 
                          summary_file_path: str,
                          param_display_names: Optional[List[str]] = None) -> tuple:
    """
    计算相对误差汇总。
    
    Args:
        param_file_path (str): 参数文件路径
        summary_file_path (str): 汇总文件保存路径
        param_display_names (list): 参数显示名称列表
        
    Returns:
        tuple: (总组数, 有效组数, 过滤组数)
    """
    # 读取数据
    df = load_from_excel(param_file_path)
    if df is None:
        raise Exception("无法读取参数文件")
    
    # 参数名称映射
    param_names_map = {
        "s-p刚度(N/mm)": "sun_stiff", "p-r刚度(N/mm)": "ring_stiff",
        "s-p阻尼(N)": "sun_damping", "p-r阻尼(N)": "ring_damping",
        "s-p力指数": "sun_exponent", "p-r力指数": "ring_exponent",
        "s-p穿透深度(mm)": "sun_dmax", "p-r穿透深度(mm)": "ring_dmax"
    }
    
    # 获取参数列名
    if not param_display_names:
        reverse_map = {v: k for k, v in param_names_map.items()}
        param_display_names = [reverse_map[col] for col in df.columns if col in reverse_map]
    
    param_code_names = [param_names_map[name] for name in param_display_names 
                       if name in param_names_map]
    
    # 验证必需列
    required_cols = param_code_names + ['试验RMS值', '仿真RMS值']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    # 计算相对误差
    df = calculate_relative_errors(df)
    
    # 计算总相对误差
    summary = calculate_total_relative_error(df, group_col=param_code_names[0])
    
    # 保存结果
    save_to_excel(summary, summary_file_path)
    
    total_groups = len(summary)
    return total_groups, total_groups, 0


# 导出的公共接口
__all__ = [
    'BatchSimulator',
    'run_batch_simulation', 
    'calculate_error_summary'
]
