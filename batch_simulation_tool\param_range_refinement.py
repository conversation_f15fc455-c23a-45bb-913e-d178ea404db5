#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据批量仿真结果与误差汇总 (error_summary.xlsx)，
自动给出新的参数上下限建议，用于下一轮参数采样。

算法思路：
1. 读取 error_summary.xlsx，分为"满足阈值 (good)" 与"不满足 (bad)" 两组。
2. 对每个参数，取 good 组的 min/max，作为核心区间。
3. 在该区间基础上外扩 margin 比例 (默认 10%)，得到新上下限。
4. 保证新范围不超出原始 batch_params.xlsx 中出现过的最小/最大值。
5. 导出 param_bounds_refined.xlsx，供用户参考。
"""

from __future__ import annotations

import os
import argparse
from typing import Tuple

import numpy as np
import pandas as pd

# 与 UI 中保持一致的映射
PARAM_DISPLAY2CODE = {
    "s-p刚度(N/mm)": "sun_stiff", "p-r刚度(N/mm)": "ring_stiff",
    "s-p阻尼(N)": "sun_damping", "p-r阻尼(N)": "ring_damping",
    "s-p力指数": "sun_exponent", "p-r力指数": "ring_exponent",
    "s-p穿透深度(mm)": "sun_dmax", "p-r穿透深度(mm)": "ring_dmax",
}
CODE2DISPLAY = {v: k for k, v in PARAM_DISPLAY2CODE.items()}


def load_original_bounds(batch_param_path: str) -> dict[str, Tuple[float, float]]:
    """返回每个参数在原始 batch_params.xlsx 中的 (min, max)。"""
    df = pd.read_excel(batch_param_path)
    bounds: dict[str, Tuple[float, float]] = {}
    for code in CODE2DISPLAY.keys():
        if code in df.columns:
            bounds[code] = (df[code].min(), df[code].max())
    return bounds


def refine_bounds(
    batch_param_path: str,
    error_summary_path: str,
    threshold: float,
    margin_ratio: float = 0.1,
) -> pd.DataFrame:
    """核心算法，返回 DataFrame: param, new_low, new_high, orig_low, orig_high"""
    df_err = pd.read_excel(error_summary_path)
    if "总相对误差" not in df_err.columns:
        raise ValueError("error_summary.xlsx 缺少列: 总相对误差")

    orig_bounds = load_original_bounds(batch_param_path)

    # good / bad 分组
    good = df_err[df_err["总相对误差"] <= threshold]
    bad = df_err[df_err["总相对误差"] > threshold]

    results = []
    q_low, q_high = 0.05, 0.95  # 可后续转为参数

    for code, (orig_low, orig_high) in orig_bounds.items():
        if code not in df_err.columns:
            continue

        # good 分布
        if not good.empty:
            g_low = good[code].quantile(q_low)
            g_high = good[code].quantile(q_high)
        else:
            g_low, g_high = orig_low, orig_high

        # bad 影响：若 bad 显示某边界完全无效，将范围远离 bad
        if not bad.empty:
            # 所有 bad 中小于 g_low 的最大值
            lower_bad_max = bad[bad[code] < g_low][code].max() if not bad[bad[code] < g_low].empty else None
            # 所有 bad 中大于 g_high 的最小值
            upper_bad_min = bad[bad[code] > g_high][code].min() if not bad[bad[code] > g_high].empty else None
        else:
            lower_bad_max = upper_bad_min = None

        # 基础范围：g_low ~ g_high
        span = g_high - g_low
        if span == 0:
            span = max(abs(g_low) * 0.05, 1e-6)

        new_low = g_low - margin_ratio * span
        new_high = g_high + margin_ratio * span

        # 利用 bad 信息进一步裁剪
        if lower_bad_max is not None:
            new_low = max(new_low, lower_bad_max)
        if upper_bad_min is not None:
            new_high = min(new_high, upper_bad_min)

        # 裁剪到原始
        new_low = max(new_low, orig_low)
        new_high = min(new_high, orig_high)

        if new_low >= new_high:
            # 回退为原始或稍作调整
            new_low = orig_low
            new_high = orig_high

        results.append({
            "参数": CODE2DISPLAY.get(code, code),
            "param_code": code,
            "orig_low": orig_low,
            "orig_high": orig_high,
            "good_q05": g_low,
            "good_q95": g_high,
            "new_low": new_low,
            "new_high": new_high,
        })

    return pd.DataFrame(results)


def main():  # pragma: no cover
    parser = argparse.ArgumentParser(description="根据误差结果自动收缩参数范围")
    parser.add_argument('batch_params', help='batch_params.xlsx 文件路径')
    parser.add_argument('error_summary', help='error_summary.xlsx 文件路径')
    parser.add_argument('--threshold', type=float, default=2.0, help='总相对误差阈值')
    parser.add_argument('--margin', type=float, default=0.1, help='在好组合 min/max 基础上外扩比例')
    args = parser.parse_args()

    df = refine_bounds(args.batch_params, args.error_summary, threshold=args.threshold, margin_ratio=args.margin)

    out_path = os.path.join(os.path.dirname(args.error_summary), 'param_bounds_refined.xlsx')
    df.to_excel(out_path, index=False)
    print(f"已生成新的参数上下限建议文件: {out_path}")
    print(df[['参数', 'new_low', 'new_high']].to_string(index=False))


if __name__ == '__main__':
    main() 