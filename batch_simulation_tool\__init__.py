# Adams 批量仿真工具包
# 包含批量仿真相关的所有功能模块

__version__ = "1.0.0"
__author__ = "Adams Simulation Team"

from .batch_simulation_ui import BatchSimulationUI
from .batch_simulation_logic import run_adams_batch_simulation
from .adams_utils import send_adams_command, process_simulation_results

__all__ = [
    'BatchSimulationUI',
    'run_adams_batch_simulation', 
    'send_adams_command',
    'process_simulation_results'
] 