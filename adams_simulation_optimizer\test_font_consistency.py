#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体一致性 - 验证所有界面元素的字体大小是否一致
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_font_consistency():
    """测试字体一致性"""
    print("=" * 60)
    print("测试界面字体一致性")
    print("=" * 60)
    
    try:
        from config import AppConfig
        print(f"✅ 默认字体大小: {AppConfig.FONT_SIZE}pt")
        print(f"✅ 默认字体族: {AppConfig.FONT_FAMILY}")
        
        # 测试界面组件导入
        from simulation.simulation_ui import SimulationWidget
        from optimization.optimization_ui import OptimizationWidget
        print("✅ 界面组件导入成功")
        
        # 检查样式配置
        from config import StyleConfig
        print("✅ 样式配置加载成功")
        
        print("\n📋 字体设置总结:")
        print(f"• 默认字体大小: {AppConfig.FONT_SIZE}pt")
        print("• 参数说明字体: 12pt (稍大，便于阅读)")
        print("• 表格内容字体: 11pt (与默认一致)")
        print("• 表格表头字体: 11pt + 粗体")
        print("• 标题字体: 默认大小 + 粗体 + 蓝色")
        
        print("\n🎯 字体层次结构:")
        print("1. 标题文字: 默认大小 + 粗体 + 颜色突出")
        print("2. 参数说明: 12pt (稍大，便于阅读)")
        print("3. 表格内容: 11pt (与界面默认一致)")
        print("4. 普通文本: 11pt (默认大小)")
        
        print("\n✅ 字体一致性测试完成！")
        print("所有界面元素的字体大小已统一设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_font_consistency()
    sys.exit(0 if success else 1)
