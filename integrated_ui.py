#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合界面 - 将批量仿真工具和优化工具整合到一个界面中
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QTabWidget, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

# 导入共享样式
from shared_styles import apply_unified_style

# 添加子模块路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'batch_simulation_tool'))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'optimization'))

try:
    from batch_simulation_ui import BatchSimulationUI
    BATCH_SIMULATION_AVAILABLE = True
    print("成功导入批量仿真模块")
except ImportError as e:
    print(f"批量仿真模块导入失败: {e}")
    print("提示：如果缺少 pythoncom 模块，请运行: pip install pywin32")
    BATCH_SIMULATION_AVAILABLE = False

try:
    from optimization_ui import OptimizationUI
    OPTIMIZATION_AVAILABLE = True
    print("成功导入优化模块")
except ImportError as e:
    print(f"优化模块导入失败: {e}")
    OPTIMIZATION_AVAILABLE = False


class IntegratedMainWindow(QMainWindow):
    """整合主界面"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Adams 仿真与优化工具集成平台")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置应用程序图标（如果有的话）
        # self.setWindowIcon(QIcon("icon.png"))
        
        self.init_ui()
        # 应用统一的样式
        apply_unified_style(self)
        
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        main_layout.addWidget(self.tab_widget)
        
        # 添加批量仿真选项卡
        if BATCH_SIMULATION_AVAILABLE:
            try:
                print("正在创建批量仿真界面...")
                # 创建一个新的Widget来包装BatchSimulationUI的内容
                batch_wrapper = QWidget()
                batch_layout = QVBoxLayout(batch_wrapper)
                batch_layout.setContentsMargins(0, 0, 0, 0)

                # 创建BatchSimulationUI实例但不显示为独立窗口
                self.batch_sim_widget = BatchSimulationUI()
                self.batch_sim_widget.hide()  # 隐藏独立窗口

                # 获取中心部件并重新设置父级
                batch_central = self.batch_sim_widget.centralWidget()
                batch_central.setParent(batch_wrapper)
                batch_layout.addWidget(batch_central)

                self.tab_widget.addTab(batch_wrapper, "批量仿真")
                print("批量仿真界面创建成功")
            except Exception as e:
                print(f"创建批量仿真界面失败: {e}")
                import traceback
                traceback.print_exc()
                self.add_error_tab("批量仿真", f"模块加载失败: {e}")
        else:
            self.add_error_tab("批量仿真", "批量仿真模块不可用")

        # 添加优化选项卡
        if OPTIMIZATION_AVAILABLE:
            try:
                print("正在创建优化界面...")
                # 创建一个新的Widget来包装OptimizationUI的内容
                opt_wrapper = QWidget()
                opt_layout = QVBoxLayout(opt_wrapper)
                opt_layout.setContentsMargins(0, 0, 0, 0)

                # 创建OptimizationUI实例但不显示为独立窗口
                self.optimization_widget = OptimizationUI()
                self.optimization_widget.hide()  # 隐藏独立窗口

                # 获取中心部件并重新设置父级
                opt_central = self.optimization_widget.centralWidget()
                opt_central.setParent(opt_wrapper)
                opt_layout.addWidget(opt_central)

                self.tab_widget.addTab(opt_wrapper, "参数优化")
                print("优化界面创建成功")
            except Exception as e:
                print(f"创建优化界面失败: {e}")
                import traceback
                traceback.print_exc()
                self.add_error_tab("参数优化", f"模块加载失败: {e}")
        else:
            self.add_error_tab("参数优化", "优化模块不可用")
            
        # 设置默认选中第一个选项卡
        self.tab_widget.setCurrentIndex(0)
        
        # 连接选项卡切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
    def add_error_tab(self, tab_name, error_message):
        """添加错误提示选项卡"""
        error_widget = QWidget()
        error_layout = QVBoxLayout(error_widget)
        
        from PyQt5.QtWidgets import QLabel
        error_label = QLabel(f"错误: {error_message}")
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setStyleSheet("color: red; font-size: 14px; padding: 50px;")
        
        error_layout.addWidget(error_label)
        self.tab_widget.addTab(error_widget, tab_name)
        
    def on_tab_changed(self, index):
        """选项卡切换事件处理"""
        tab_text = self.tab_widget.tabText(index)
        self.statusBar().showMessage(f"当前选项卡: {tab_text}")
        

        
    def closeEvent(self, event):
        """应用程序关闭事件处理"""
        try:
            # 如果批量仿真界面存在且有正在运行的任务，先停止
            if hasattr(self, 'batch_sim_widget') and hasattr(self.batch_sim_widget, 'batch_running'):
                if self.batch_sim_widget.batch_running:
                    reply = QMessageBox.question(
                        self, 
                        '确认退出', 
                        '批量仿真正在运行中，确定要退出吗？',
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        event.ignore()
                        return
                    
                    # 停止批量仿真
                    if hasattr(self.batch_sim_widget, 'worker') and self.batch_sim_widget.worker:
                        self.batch_sim_widget.worker.stop_flag = True
                        
            # 如果优化界面存在且有正在运行的任务，先停止
            if hasattr(self, 'optimization_widget') and hasattr(self.optimization_widget, 'worker'):
                if hasattr(self.optimization_widget.worker, 'isRunning') and self.optimization_widget.worker.isRunning():
                    reply = QMessageBox.question(
                        self, 
                        '确认退出', 
                        '参数优化正在运行中，确定要退出吗？',
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        event.ignore()
                        return
                        
                    # 停止优化
                    self.optimization_widget.worker.requestInterruption()
                    
        except Exception as e:
            print(f"关闭时发生错误: {e}")
        finally:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("模型修正模块")
    app.setApplicationVersion("1.0.0")
    
    # 创建并显示主窗口
    window = IntegratedMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
