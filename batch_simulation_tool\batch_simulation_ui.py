import sys
import os
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QFileDialog, QTableWidget, QTableWidgetItem,
                             QSpinBox, QDoubleSpinBox, QMessageBox,
                             QGroupBox, QProgressBar, QScrollArea, QHeaderView, QCheckBox, QDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont

# 尝试导入共享样式，如果失败则使用本地样式
try:
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from shared_styles import apply_unified_style, UNIFIED_STYLESHEET
    SHARED_STYLES_AVAILABLE = True
except ImportError:
    SHARED_STYLES_AVAILABLE = False

from scipy.stats import qmc
from itertools import product

# 导入批量仿真逻辑 (修正)
from batch_simulation_logic import run_adams_batch_simulation, calculate_relative_error_summary

class BatchSimulationWorker(QThread):
    progress_update = pyqtSignal(int, str)
    batch_finished = pyqtSignal()
    batch_error = pyqtSignal(str)

    def __init__(self, save_base_path, sim_time, sim_steps, parent=None):
        super().__init__(parent)
        self.save_base_path = save_base_path
        self.sim_time = sim_time
        self.sim_steps = sim_steps
        self.stop_flag = False  # 改为实例变量

    def run(self):
        try:
            run_adams_batch_simulation(
                save_base_path=self.save_base_path,
                sim_time=self.sim_time,
                sim_steps=self.sim_steps,
                param_combos=[], # param_combos is no longer used, logic reads from file
                progress_callback=self.progress_update.emit,
                stop_flag_check=lambda: self.stop_flag
            )
            self.batch_finished.emit()
        except Exception as e:
            self.batch_error.emit(f"批量仿真执行失败: {e}")

class ProgressDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量仿真进度")
        # 设置为非模态窗口，允许用户继续与主界面交互，同时添加最小化按钮
        self.setModal(False)
        self.setWindowFlags(self.windowFlags() | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        self.resize(400, 100)
        layout = QVBoxLayout(self)
        self.progress_bar = QProgressBar(self)
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.status_label = QLabel("状态：准备中...", self)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.status_label)

    def update_progress(self, value, text):
        self.progress_bar.setValue(value)
        self.status_label.setText(f"状态：{text}")

class BatchSimulationUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Adams 批量仿真工具")
        self.setGeometry(100, 100, 1200, 800)  # 增大窗口尺寸以显示更多内容
        
        self.batch_param_combos = []
        self.batch_running = False
        self.worker = None  # 初始化worker为None
        self.progress_dialog = None  # 初始化进度对话框为None

        # 创建默认保存路径
        try:
            self.batch_save_base_path = os.path.join(os.getcwd(), "AdamsBatchSimulationResults")
            os.makedirs(self.batch_save_base_path, exist_ok=True)
        except Exception as e:
            self.batch_save_base_path = os.getcwd()
            print(f"创建默认保存路径失败，使用当前目录: {e}")
        
        # 设置默认仿真参数
        self.default_sim_time = 0.5
        self.default_sim_steps = 10000

        self.init_ui()
        # 应用统一样式
        if SHARED_STYLES_AVAILABLE:
            apply_unified_style(QApplication.instance())
        else:
            QApplication.setFont(QFont("Microsoft YaHei UI", 11))

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)  # 改回垂直布局

        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        scroll.setWidget(content_widget)
        main_layout.addWidget(scroll)

        # 结果保存路径组
        path_group = QGroupBox("结果保存路径")
        path_layout = QHBoxLayout()
        self.save_path_edit = QLineEdit(self.batch_save_base_path)
        browse_btn = QPushButton("浏览...")
        browse_btn.clicked.connect(self.browse_save_path)
        path_layout.addWidget(self.save_path_edit)
        path_layout.addWidget(browse_btn)
        path_group.setLayout(path_layout)
        content_layout.addWidget(path_group)

        # 批量仿真配置组
        config_group = QGroupBox("批量仿真配置")
        config_layout = QVBoxLayout()
        
        # 参数范围设置表格
        self.param_table = QTableWidget(8, 5)
        self.param_table.setHorizontalHeaderLabels(["参数名称", "默认值", "下限", "上限", "参与采样"])

        # 设置表格的最小高度以确保所有行都能显示
        self.param_table.setMinimumHeight(280)  # 8行 * 35像素/行 = 280像素

        # 设置列宽策略：参数名称列稍宽，其他列平均分配
        header = self.param_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 参数名称列自适应内容
        for i in range(1, 5):
            header.setSectionResizeMode(i, QHeaderView.Stretch)  # 其他列平均分配

        self._setup_param_table_row(0, "s-p刚度(N/mm)", 360000, 400000, decimals=0)
        self._setup_param_table_row(1, "p-r刚度(N/mm)", 740000, 780000, decimals=0)
        self._setup_param_table_row(2, "s-p阻尼(N)", 0, 50, decimals=0)
        self._setup_param_table_row(3, "p-r阻尼(N)", 0, 80, decimals=0)
        self._setup_param_table_row(4, "s-p力指数", 1.5, 3, decimals=2)
        self._setup_param_table_row(5, "p-r力指数", 1.5, 3, decimals=2)
        self._setup_param_table_row(6, "s-p穿透深度(mm)", 0.05, 0.2, decimals=3)
        self._setup_param_table_row(7, "p-r穿透深度(mm)", 0.05, 0.2, decimals=3)

        # 调整行高以确保内容完整显示
        self.param_table.verticalHeader().setDefaultSectionSize(30)

        config_layout.addWidget(self.param_table)

        # 工况输入表格
        condition_group = QGroupBox("工况设置")
        condition_layout = QVBoxLayout()
        self.condition_table = QTableWidget(3, 3)  # 增加到3行以显示示例
        self.condition_table.setHorizontalHeaderLabels(["转速(rpm)", "负载扭矩(N·m)", "试验RMS值"])

        # 设置工况表格的最小高度
        self.condition_table.setMinimumHeight(150)  # 增加高度以容纳更多行

        # 设置列宽策略
        condition_header = self.condition_table.horizontalHeader()
        condition_header.setSectionResizeMode(QHeaderView.Stretch)

        # 调整行高
        self.condition_table.verticalHeader().setDefaultSectionSize(30)

        # 添加示例数据
        self._setup_condition_examples()

        # 添加工况表格输入验证
        self.condition_table.itemChanged.connect(self._validate_condition_input)

        condition_layout.addWidget(self.condition_table)
        cond_btn_layout = QHBoxLayout()
        add_cond_btn = QPushButton("添加工况"); del_cond_btn = QPushButton("删除工况")
        add_cond_btn.clicked.connect(lambda: self.condition_table.insertRow(self.condition_table.rowCount()))
        del_cond_btn.clicked.connect(lambda: self.condition_table.removeRow(self.condition_table.currentRow()) if self.condition_table.rowCount() > 1 else None)
        cond_btn_layout.addWidget(add_cond_btn); cond_btn_layout.addWidget(del_cond_btn)
        condition_layout.addLayout(cond_btn_layout)
        condition_group.setLayout(condition_layout)
        config_layout.addWidget(condition_group)
        
        # 样本数和总数显示
        samples_layout = QHBoxLayout()
        samples_layout.addWidget(QLabel("参数样本数:"))
        self.total_samples_spin = QSpinBox()
        self.total_samples_spin.setRange(1, 10000); self.total_samples_spin.setValue(20)
        samples_layout.addWidget(self.total_samples_spin)
        self.total_sim_label = QLabel()
        samples_layout.addWidget(self.total_sim_label)
        samples_layout.addStretch(1)
        config_layout.addLayout(samples_layout)
        # 联动更新
        self.total_samples_spin.valueChanged.connect(self.update_total_sim_label)
        self.condition_table.model().rowsInserted.connect(self.update_total_sim_label)
        self.condition_table.model().rowsRemoved.connect(self.update_total_sim_label)
        # 监听表格内容变化
        self.condition_table.itemChanged.connect(self.update_total_sim_label)
        self.update_total_sim_label()

        # 操作按钮
        btn_layout = QHBoxLayout()
        generate_btn = QPushButton("生成参数文件"); self.run_batch_btn = QPushButton("启动批量仿真")
        self.calc_error_btn = QPushButton("计算相对误差")
        generate_btn.clicked.connect(self.generate_batch_params_file)
        self.run_batch_btn.clicked.connect(self.toggle_batch_simulation)
        self.calc_error_btn.clicked.connect(self.calculate_relative_error)
        btn_layout.addWidget(generate_btn)
        btn_layout.addWidget(self.run_batch_btn)
        btn_layout.addWidget(self.calc_error_btn)
        config_layout.addLayout(btn_layout)

        # 进度和状态（移除主界面进度条）
        self.status_label = QLabel("状态：待命")
        config_layout.addWidget(self.status_label)
        
        config_group.setLayout(config_layout)
        content_layout.addWidget(config_group)

        # 内容顶部对齐并在底部加弹性伸展以避免大片留白
        content_layout.addStretch(1)

        # 应用统一样式（如果共享样式不可用，则使用本地样式）
        if not SHARED_STYLES_AVAILABLE:
            _APP_QSS = """
            QGroupBox {
                border: 1px solid #EDEBE9;
                border-radius: 4px;
                margin-top: 15px;
                padding-top: 20px;
            }
            QGroupBox:title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                color: #0078D4;
                font-weight: bold;
            }
            QPushButton {
                background-color: #0078D4;
                color: #FFFFFF;
                border: 1px solid #0078D4;
                border-radius: 4px;
                padding: 4px 12px;
            }
            QPushButton:hover {
                background-color: #106EBE;
                border-color: #106EBE;
            }
            QPushButton:pressed {
                background-color: #005A9E;
                border-color: #005A9E;
            }
            QPushButton:disabled {
                background-color: #6C757D;
                border-color: #6C757D;
                color: #A19F9D;
            }
            QTableWidget, QHeaderView::section {
                border: 1px solid #E1DFDD;
            }
            QHeaderView::section:horizontal {
                background-color: #0078D4;
                color: #FFFFFF;
            }
            QHeaderView::section:vertical {
                background-color: #FFFFFF;
                color: #323130;
            }
            QLineEdit, QSpinBox, QDoubleSpinBox {
                border: 1px solid #E1DFDD;
                border-radius: 4px;
                padding: 2px;
            }
            QMainWindow {
                background-color: #F3F2F1;
            }
            QLabel {
                color: #323130;
            }
            """
            QApplication.instance().setStyleSheet(_APP_QSS)

    def _setup_param_table_row(self, row, name, low, high, decimals):
        self.param_table.setItem(row, 0, QTableWidgetItem(name))
        default_val = (low + high) / 2
        for col, val in [(1, default_val), (2, low), (3, high)]:
            spin_box = QDoubleSpinBox()
            spin_box.setRange(0, 1e9); spin_box.setDecimals(decimals); spin_box.setValue(val)  # 限制为0或正数
            self.param_table.setCellWidget(row, col, spin_box)
        
        checkbox = QCheckBox(); checkbox.setChecked(True)
        widget = QWidget(); layout = QHBoxLayout(widget)
        layout.addWidget(checkbox); layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(0,0,0,0)
        self.param_table.setCellWidget(row, 4, widget)
        
        min_box = self.param_table.cellWidget(row, 2)
        max_box = self.param_table.cellWidget(row, 3)
        checkbox.stateChanged.connect(lambda state, b1=min_box, b2=max_box: (b1.setEnabled(state==Qt.Checked), b2.setEnabled(state==Qt.Checked)))

    def _setup_condition_examples(self):
        """设置工况表格的示例数据"""
        # 示例工况数据
        examples = [
            ("1500", "100", "0.85"),    # 第一行示例
            ("2000", "150", "1.20"),    # 第二行示例
            ("2500", "200", "1.55")     # 第三行示例
        ]

        for row, (speed, torque, rms) in enumerate(examples):
            self.condition_table.setItem(row, 0, QTableWidgetItem(speed))
            self.condition_table.setItem(row, 1, QTableWidgetItem(torque))
            self.condition_table.setItem(row, 2, QTableWidgetItem(rms))

    def _validate_condition_input(self, item):
        """验证工况表格输入，确保只能输入0或正数"""
        if item is None:
            return

        text = item.text().strip()
        if not text:  # 空值允许
            return

        try:
            value = float(text)
            if value < 0:
                # 输入了负数，显示错误并清空
                row = item.row() + 1
                col_names = ["转速", "负载扭矩", "试验RMS值"]
                col_name = col_names[item.column()]

                QMessageBox.warning(self, "输入错误",
                                  f"第 {row} 行 {col_name} 不能为负数！\n请输入0或正数。")

                # 暂时断开信号连接，避免递归
                self.condition_table.itemChanged.disconnect(self._validate_condition_input)
                item.setText("")  # 清空错误输入
                self.condition_table.itemChanged.connect(self._validate_condition_input)

        except ValueError:
            # 输入了非数字，显示错误并清空
            row = item.row() + 1
            col_names = ["转速", "负载扭矩", "试验RMS值"]
            col_name = col_names[item.column()]

            QMessageBox.warning(self, "输入错误",
                              f"第 {row} 行 {col_name} 必须是数字！\n请输入有效的数值。")

            # 暂时断开信号连接，避免递归
            self.condition_table.itemChanged.disconnect(self._validate_condition_input)
            item.setText("")  # 清空错误输入
            self.condition_table.itemChanged.connect(self._validate_condition_input)

    def browse_save_path(self):
        directory = QFileDialog.getExistingDirectory(self, "选择保存目录", self.save_path_edit.text())
        if directory: self.save_path_edit.setText(directory)

    def _count_complete_conditions(self):
        """计算完整填写的工况数量（只计算有效的正数工况）"""
        complete_count = 0
        for row in range(self.condition_table.rowCount()):
            speed_item = self.condition_table.item(row, 0)
            torque_item = self.condition_table.item(row, 1)
            rms_item = self.condition_table.item(row, 2)

            # 检查所有三个字段是否都有内容且不为空
            if (speed_item and speed_item.text().strip() and
                torque_item and torque_item.text().strip() and
                rms_item and rms_item.text().strip()):
                try:
                    # 尝试转换为数字并验证是否为0或正数
                    speed = float(speed_item.text().strip())
                    torque = float(torque_item.text().strip())
                    exp_rms = float(rms_item.text().strip())

                    # 只有当所有值都是0或正数时才计入完整工况
                    if speed >= 0 and torque >= 0 and exp_rms >= 0:
                        complete_count += 1
                except ValueError:
                    # 如果转换失败，不计入完整工况
                    continue
        return complete_count

    def update_total_sim_label(self):
        n = self.total_samples_spin.value()
        # 只计算完整填写的工况数量
        m = self._count_complete_conditions()
        self.total_sim_label.setText(f"  (实际仿真总数: {n*m})")

    def generate_batch_params_file(self):
        # 1. 获取参数采样配置并验证
        param_info = []
        param_names_map = {"s-p刚度(N/mm)": "sun_stiff", "p-r刚度(N/mm)": "ring_stiff", "s-p阻尼(N)": "sun_damping", "p-r阻尼(N)": "ring_damping", "s-p力指数": "sun_exponent", "p-r力指数": "ring_exponent", "s-p穿透深度(mm)": "sun_dmax", "p-r穿透深度(mm)": "ring_dmax"}

        for row in range(self.param_table.rowCount()):
            param_name = self.param_table.item(row, 0).text()
            is_sampled_widget = self.param_table.cellWidget(row, 4)
            is_sampled = is_sampled_widget.findChild(QCheckBox).isChecked()

            default_val = self.param_table.cellWidget(row, 1).value()
            low_val = self.param_table.cellWidget(row, 2).value()
            high_val = self.param_table.cellWidget(row, 3).value()

            # 验证参数值必须为0或正数
            if default_val < 0:
                QMessageBox.warning(self, "参数错误", f"参数 '{param_name}' 的默认值 ({default_val}) 不能为负数！\n请输入0或正数。")
                return
            if low_val < 0:
                QMessageBox.warning(self, "参数错误", f"参数 '{param_name}' 的下限值 ({low_val}) 不能为负数！\n请输入0或正数。")
                return
            if high_val < 0:
                QMessageBox.warning(self, "参数错误", f"参数 '{param_name}' 的上限值 ({high_val}) 不能为负数！\n请输入0或正数。")
                return
            if low_val > high_val:
                QMessageBox.warning(self, "参数错误", f"参数 '{param_name}' 的下限值 ({low_val}) 不能大于上限值 ({high_val})！")
                return

            param_info.append({
                "name": param_names_map[param_name],
                "is_sampled": is_sampled,
                "default": default_val,
                "low": low_val,
                "high": high_val,
            })

        # 2. 拉丁超立方采样
        sampled_params = [p for p in param_info if p["is_sampled"]]
        fixed_params = {p["name"]: p["default"] for p in param_info if not p["is_sampled"]}
        if not sampled_params:
            QMessageBox.warning(self, "提示", "至少选择一个参数参与采样。")
            return

        n_samples = self.total_samples_spin.value()
        if n_samples <= 0:
            QMessageBox.warning(self, "参数错误", "参数样本数必须大于0！")
            return

        try:
            sampler = qmc.LatinHypercube(d=len(sampled_params))
            sample_scaled = qmc.scale(sampler.random(n=n_samples),
                                      [p["low"] for p in sampled_params],
                                      [p["high"] for p in sampled_params])
        except Exception as e:
            QMessageBox.critical(self, "采样错误", f"生成参数样本时发生错误: {e}")
            return
        
        param_combos = []
        for sample in sample_scaled:
            p_dict = dict(zip([p["name"] for p in sampled_params], sample))
            p_dict.update(fixed_params)
            param_combos.append(p_dict)
        
        # 3. 获取工况（只处理完整的工况）
        conditions = []
        incomplete_rows = []

        for row in range(self.condition_table.rowCount()):
            speed_item = self.condition_table.item(row, 0)
            torque_item = self.condition_table.item(row, 1)
            rms_item = self.condition_table.item(row, 2)

            # 检查是否为完整工况
            if (speed_item and speed_item.text().strip() and
                torque_item and torque_item.text().strip() and
                rms_item and rms_item.text().strip()):
                try:
                    speed = float(speed_item.text().strip())
                    torque = float(torque_item.text().strip())
                    exp_rms = float(rms_item.text().strip())

                    # 验证工况参数必须为0或正数
                    if speed < 0:
                        QMessageBox.warning(self, "工况参数错误", f"第 {row + 1} 行转速 ({speed}) 不能为负数！\n请输入0或正数。")
                        return
                    if torque < 0:
                        QMessageBox.warning(self, "工况参数错误", f"第 {row + 1} 行负载扭矩 ({torque}) 不能为负数！\n请输入0或正数。")
                        return
                    if exp_rms < 0:
                        QMessageBox.warning(self, "工况参数错误", f"第 {row + 1} 行试验RMS值 ({exp_rms}) 不能为负数！\n请输入0或正数。")
                        return

                    conditions.append({"工况_转速(rpm)": speed, "工况_负载扭矩(N·m)": torque, "试验RMS值": exp_rms})
                except ValueError:
                    incomplete_rows.append(row + 1)
            else:
                # 如果有任何一个字段为空，记录为不完整行
                if (speed_item and speed_item.text().strip()) or \
                   (torque_item and torque_item.text().strip()) or \
                   (rms_item and rms_item.text().strip()):
                    incomplete_rows.append(row + 1)

        # 检查是否有完整的工况
        if not conditions:
            if incomplete_rows:
                QMessageBox.warning(self, "警告", f"第 {', '.join(map(str, incomplete_rows))} 行工况数据不完整！\n请确保所有字段都已正确填写，或删除不需要的行。")
            else:
                QMessageBox.warning(self, "警告", "请至少输入一组完整工况！")
            return

        # 如果有不完整的工况，给出提示但继续处理
        if incomplete_rows:
            QMessageBox.information(self, "提示", f"第 {', '.join(map(str, incomplete_rows))} 行工况数据不完整，将被跳过。\n将使用 {len(conditions)} 组完整工况进行仿真。")

        # 4. 生成笛卡尔积并保存
        all_combos = [dict(p, **c) for p, c in product(param_combos, conditions)]
        save_path = self.save_path_edit.text().strip()
        if not save_path:
            QMessageBox.warning(self, "警告", "请选择保存路径！")
            return

        # 验证保存路径
        try:
            os.makedirs(save_path, exist_ok=True)
            # 测试写入权限
            test_file = os.path.join(save_path, "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            QMessageBox.critical(self, "路径错误", f"无法访问保存路径: {save_path}\n错误: {e}")
            return

        file_path = os.path.join(save_path, "batch_params.xlsx")

        try:
            import pandas as pd
            df = pd.DataFrame(all_combos)
            df["仿真RMS值"] = ""
            df.to_excel(file_path, index=False)
            QMessageBox.information(self, "成功", f"成功生成 {len(all_combos)} 组仿真任务，文件已保存至:\n{file_path}")
            self.status_label.setText("状态：参数文件已生成，可以启动仿真")
            self.calc_error_btn.setEnabled(True)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存文件失败: {e}")

    def toggle_batch_simulation(self):
        if self.batch_running:
            if hasattr(self, 'worker') and self.worker.isRunning():
                self.worker.stop_flag = True
            self.run_batch_btn.setText("启动批量仿真")
            self.batch_running = False
            self.status_label.setText("状态：已暂停")
            # 新增：中断时关闭进度对话框
            if self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None
        else:
            save_path = self.save_path_edit.text().strip()
            if not save_path:
                QMessageBox.warning(self, "警告", "请先选择保存路径！")
                return

            param_file = os.path.join(save_path, "batch_params.xlsx")
            if not os.path.exists(param_file):
                QMessageBox.warning(self, "警告", "找不到参数文件 'batch_params.xlsx'！\n请先生成参数文件。")
                return

            # 检查参数文件是否有效
            try:
                import pandas as pd
                df = pd.read_excel(param_file)
                if len(df) == 0:
                    QMessageBox.warning(self, "警告", "参数文件为空！\n请重新生成参数文件。")
                    return
            except Exception as e:
                QMessageBox.critical(self, "文件错误", f"无法读取参数文件: {e}")
                return

            self.batch_running = True
            self.run_batch_btn.setText("暂停仿真")
            self.status_label.setText("状态：批量仿真进行中...")
            
            self.worker = BatchSimulationWorker(
                save_path,
                0.5,    # 仿真时间写死为0.5秒
                10000   # 仿真步数写死为10000步
            )
            self.worker.progress_update.connect(self.update_batch_progress_dialog)
            self.worker.batch_finished.connect(self.handle_batch_finished)
            self.worker.batch_error.connect(self.handle_batch_error)
            self.worker.start()

            # 新增：弹出进度对话框
            self.progress_dialog = ProgressDialog(self)
            self.progress_dialog.show()

    def update_batch_progress_dialog(self, progress, status_text):
        if self.progress_dialog:
            self.progress_dialog.update_progress(progress, status_text)
        # 只要开始有结果输出（progress>0），就允许用户计算相对误差
        if progress > 0 and not self.calc_error_btn.isEnabled():
            self.calc_error_btn.setEnabled(True)

    def handle_batch_finished(self):
        self.status_label.setText("状态：批量仿真完成！可计算相对误差。")
        self.run_batch_btn.setText("启动批量仿真")
        self.batch_running = False
        self.calc_error_btn.setEnabled(True)
        if self.progress_dialog:
            self.progress_dialog.update_progress(100, "批量仿真完成")
            self.progress_dialog.accept() # 关闭对话框
            self.progress_dialog = None
        QMessageBox.information(self, "完成", "批量仿真已全部完成！现在可以计算相对误差。")

    def handle_batch_error(self, message):
        self.status_label.setText(f"状态：批量仿真出错！")
        self.run_batch_btn.setText("启动批量仿真")
        self.batch_running = False
        self.calc_error_btn.setEnabled(False)
        if self.progress_dialog:
            self.progress_dialog.update_progress(0, "批量仿真出错")
            self.progress_dialog.reject() # 关闭对话框
            self.progress_dialog = None
        QMessageBox.critical(self, "批量仿真错误", message)

    def calculate_relative_error(self):
        # 1. 让用户选择参数文件 (batch_params.xlsx)
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择参数文件",
            self.save_path_edit.text() or os.getcwd(),
            "Excel Files (*.xlsx);;All Files (*)"
        )
        if not file_path:
            return  # 用户取消

        # 2. 设置汇总结果保存路径（同目录下 error_summary.xlsx）
        summary_file = os.path.join(os.path.dirname(file_path), "error_summary.xlsx")

        try:
            # 直接调用逻辑函数；让其自行推断需要分组的参数列
            total, kept, filtered = calculate_relative_error_summary(file_path, summary_file, max_total_error=None)

            if filtered > 0:
                msg = (
                    f"相对误差计算完成！\n\n"
                    f"参数组合总数: {total}\n"
                    f"满足阈值的组合: {kept}\n"
                    f"不满足阈值的组合: {filtered}\n\n"
                    f"结果已保存至: {summary_file}"
                )
            else:
                msg = (
                    f"相对误差计算完成！\n\n"
                    f"参数组合总数: {total}\n"
                    f"结果已保存至: {summary_file}"
                )

            QMessageBox.information(self, "成功", msg)
        except Exception as e:
            QMessageBox.critical(self, "计算失败", f"计算相对误差时发生错误: {e}")

    def closeEvent(self, event):
        """应用程序关闭时的清理工作"""
        try:
            # 停止正在运行的仿真线程
            if hasattr(self, 'worker') and self.worker.isRunning():
                self.worker.stop_flag = True
                self.worker.wait(3000)  # 等待最多3秒
                if self.worker.isRunning():
                    self.worker.terminate()  # 强制终止
                    self.worker.wait(1000)

            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()

        except Exception as e:
            print(f"关闭时发生错误: {e}")
        finally:
            event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = BatchSimulationUI()
    ex.show()
    sys.exit(app.exec_()) 
