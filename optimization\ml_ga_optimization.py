#!/usr/bin/env python3
"""
使用机器学习回归模型（随机森林、极端随机森林、堆叠集成）结合遗传算法，对批量仿真参数进行优化寻优。

步骤:
1. 读取 error_summary.xlsx (由 batch_simulation_tool 生成)。
2. 将参数列作为输入 X, "总相对误差" 作为输出 y。
3. 将数据按 7:3 随机划分为训练集/测试集, 训练回归模型。
4. 使用遗传算法 (pygad) 在参数空间内搜索, 以最小化模型预测的 "总相对误差"。

运行示例:
    python ml_ga_optimization.py /path/to/error_summary.xlsx  --generations 200 --pop_size 50

结果:
    - 在控制台输出测试集 R^2 / RMSE
    - 输出遗传算法最佳参数组合及其预测误差
"""

import argparse
import os
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, RandomizedSearchCV, cross_val_score
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, StackingRegressor
from sklearn.metrics import mean_squared_error, r2_score
import pygad
from scipy.stats import randint


def load_data(xlsx_path):
    """读取 Excel 并返回 (X, y, param_names)"""
    if not os.path.exists(xlsx_path):
        raise FileNotFoundError(f"文件不存在: {xlsx_path}")
    df = pd.read_excel(xlsx_path)
    if '总相对误差' not in df.columns:
        raise ValueError("文件缺少列: 总相对误差")
    y = df['总相对误差'].values
    X = df.drop(columns=['总相对误差']).values
    param_names = list(df.drop(columns=['总相对误差']).columns)
    return X, y, param_names


def build_model(model_name, X_train, y_train):
    """根据名称训练并返回模型"""
    model_name = model_name.lower()
    if model_name in {"rf", "randomforest"}:
        model = RandomForestRegressor(n_estimators=300, random_state=42)
    elif model_name in {"etr", "extratrees"}:
        # 创建一个固定参数的基准模型（原始参数）
        base_model = ExtraTreesRegressor(n_estimators=400, random_state=42)
        
        # 为 ExtraTreesRegressor 定义一个更稳健、防止过拟合的超参数搜索空间
        param_dist = {
            'n_estimators': randint(200, 800),
            'max_depth': list(range(10, 51, 10)) + [None],  # 限制最大深度，防止过拟合
            'min_samples_split': randint(2, 15),      # 增加分裂所需最小样本数
            'min_samples_leaf': randint(1, 15),       # 增加叶节点最小样本数
            'max_features': ['sqrt', 'log2', None]    # 限制特征数量，增强泛化
        }
        
        # 基础模型
        etr = ExtraTreesRegressor(random_state=42)
        
        # 使用 RandomizedSearchCV 进行 50 次迭代的 5 折交叉验证搜索
        print("\n[Auto-tuning] 开始为极端随机树模型进行自动化超参数搜索 (n_iter=50)...")
        search_model = RandomizedSearchCV(
            estimator=etr,
            param_distributions=param_dist,
            n_iter=50,       # 尝试 50 种参数组合
            cv=5,            # 5 折交叉验证
            scoring='r2',    # 使用 R² 作为评分标准
            n_jobs=-1,       # 使用所有可用核心
            random_state=42,
            verbose=0        # 设置为 1 可在控制台查看搜索进度
        )
        
        # 同时训练固定参数模型和自动搜索模型
        base_model.fit(X_train, y_train)
        search_model.fit(X_train, y_train)
        
        # 比较两个模型的交叉验证性能
        base_cv_scores = cross_val_score(base_model, X_train, y_train, cv=5, scoring='r2')
        best_model = search_model.best_estimator_
        
        print(f"[Auto-tuning] 固定参数模型的 5 折交叉验证 R²: {base_cv_scores.mean():.4f}")
        print(f"[Auto-tuning] 最佳参数: {search_model.best_params_}")
        print(f"[Auto-tuning] 自动调参模型的 5 折交叉验证 R²: {search_model.best_score_:.4f}")
        
        # 选择交叉验证分数更高的模型
        if base_cv_scores.mean() > search_model.best_score_:
            print("[Auto-tuning] 固定参数模型表现更好，将使用固定参数模型")
            model = base_model
        else:
            print("[Auto-tuning] 自动调参模型表现更好，将使用自动调参模型")
            model = best_model
        
        return model  # 返回选择的最佳模型
    elif model_name in {"stack", "stacking"}:
        # 堆叠：RF + ETR -> RF
        estimators = [
            ('rf', RandomForestRegressor(n_estimators=300, random_state=42)),
            ('etr', ExtraTreesRegressor(n_estimators=400, random_state=42))
        ]
        final_est = RandomForestRegressor(n_estimators=200, random_state=42)
        model = StackingRegressor(estimators=estimators, final_estimator=final_est)
    else:
        raise ValueError(f"不支持的模型: {model_name}")

    model.fit(X_train, y_train)

    # 如果是搜索器，打印最佳参数并返回最佳模型
    if isinstance(model, RandomizedSearchCV):
        print("[Auto-tuning] 自动化超参数搜索完成！")
        print(f"[Auto-tuning] 最佳参数: {model.best_params_}")
        print(f"[Auto-tuning] 最佳交叉验证 R²: {model.best_score_:.4f}\n")
        # RandomizedSearchCV 会自动用最佳参数在全部 X_train, y_train 上重新训练模型
        # model.best_estimator_ 就是这个训练好的最佳模型
        return model.best_estimator_

    return model


def evaluate_model(model, X_test, y_test):
    y_pred = model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test, y_pred)
    return rmse, r2


def run_ga(model, bounds, generations=200, pop_size=50, crossover_prob=0.8, mutation_prob=0.1, progress_callback=None):
    """使用 pygad 进行遗传算法优化"""
    num_params = len(bounds)

    # gene_space 可以指定每个变量的范围
    gene_space = [{'low': low, 'high': high} for low, high in bounds]

    def fitness_func(ga_instance, solution, solution_idx):
        # ga_instance 和 solution_idx 是 pygad 要求的参数，但在此处不使用
        sol = np.array(solution).reshape(1, -1)
        pred = model.predict(sol)[0]
        return -float(pred)

    # 定义每代回调用于进度报告
    def on_generation(ga_inst):
        if progress_callback is not None and generations > 0:
            pct = int(ga_inst.generations_completed / generations * 100)
            progress_callback(pct)

    ga = pygad.GA(
        gene_space=gene_space,
        num_generations=generations,
        num_parents_mating=pop_size//2,
        fitness_func=fitness_func,
        sol_per_pop=pop_size,
        num_genes=num_params,
        crossover_probability=crossover_prob,
        mutation_probability=mutation_prob,
        mutation_type="random",
        mutation_percent_genes=10,
        random_mutation_min_val=[low for low, _ in bounds],
        random_mutation_max_val=[high for _, high in bounds],
        stop_criteria=[f'saturate_{generations//10}'],
        on_generation=on_generation
    )

    ga.run()
    if progress_callback is not None:
        progress_callback(100)
    return ga.best_solution()


def optimize(xlsx_path, test_ratio=0.2, generations=200, pop_size=50, model_name="rf", progress_callback=None):
    """执行完整优化流程并返回结果。

    参数
    ------
    xlsx_path : str
        error_summary.xlsx 路径
    test_ratio : float
        测试集比例
    generations : int
        遗传算法迭代次数
    pop_size : int
        种群规模
    model_name : str
        选择的模型名称

    返回
    ----
    dict
        包含 rmse、r2、param_names、best_solution、best_pred_error
    """

    # 1. 读取数据
    X, y, param_names = load_data(xlsx_path)

    # 2. 训练/测试划分
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=test_ratio, random_state=42
    )

    # 3. 训练模型
    model = build_model(model_name, X_train, y_train)

    # 4. 性能评估 & 预测
    y_pred_test = model.predict(X_test)

    mse = mean_squared_error(y_test, y_pred_test)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_test, y_pred_test)

    # 5. 定义搜索边界 (历史范围外扩 10%)
    mins = X.min(axis=0)
    maxs = X.max(axis=0)
    span = maxs - mins
    bounds = [
        (low - 0.1 * sp, high + 0.1 * sp)
        for low, high, sp in zip(mins, maxs, span)
    ]

    # 6. 遗传算法优化
    best_sol, _, _ = run_ga(
        model,
        bounds,
        generations=generations,
        pop_size=pop_size,
        progress_callback=progress_callback,
    )

    best_pred_error = model.predict(best_sol.reshape(1, -1))[0]

    return {
        "rmse": rmse,
        "r2": r2,
        "param_names": param_names,
        "best_solution": best_sol,
        "best_pred_error": best_pred_error,
    }


def main():
    parser = argparse.ArgumentParser(description="机器学习 + GA 优化")
    parser.add_argument('xlsx', help='error_summary.xlsx 文件路径')
    parser.add_argument('--test_ratio', type=float, default=0.2, help='测试集比例 (默认0.2)')
    parser.add_argument('--generations', type=int, default=200, help='遗传算法迭代代数')
    parser.add_argument('--pop_size', type=int, default=50, help='遗传算法种群规模')
    parser.add_argument('--model', type=str, default='rf',
                        choices=['rf', 'etr', 'stack'],
                        help='选择用于拟合的代理模型')
    args = parser.parse_args()

    X, y, param_names = load_data(args.xlsx)

    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=args.test_ratio, random_state=42)

    model = build_model(args.model, X_train, y_train)
    rmse, r2 = evaluate_model(model, X_test, y_test)
    print(f"测试集 RMSE: {rmse:.4f}, R^2: {r2:.4f}")

    # 参数边界: 以历史数据 min/max 为范围，外扩 10%
    mins = X.min(axis=0)
    maxs = X.max(axis=0)
    span = maxs - mins
    bounds = [(low - 0.1*sp, high + 0.1*sp) for low, high, sp in zip(mins, maxs, span)]

    best_sol, _, _ = run_ga(
        model,
        bounds,
        generations=args.generations,
        pop_size=args.pop_size
    )

    best_pred_error = model.predict(best_sol.reshape(1, -1))[0]

    print("\n=== 最佳参数组合 ===")
    for name, val in zip(param_names, best_sol):
        print(f"{name}: {val:.6g}")
    print(f"预测的总相对误差: {best_pred_error:.6g}")


if __name__ == '__main__':
    main() 