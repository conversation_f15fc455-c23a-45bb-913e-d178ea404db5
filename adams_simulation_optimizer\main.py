#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Adams 仿真与优化工具集成平台 - 主入口文件

这是程序的唯一入口点，负责启动整个应用程序。
集成了批量仿真和参数优化两大功能模块。

作者: Adams Simulation Team
版本: 2.0.0
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入配置和主窗口
try:
    from config import AppConfig, apply_global_style
    from gui.main_window import MainWindow
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖模块都在正确的位置")
    sys.exit(1)


def check_dependencies():
    """检查必要的依赖包是否已安装"""
    required_packages = [
        'PyQt5',
        'numpy', 
        'pandas',
        'scipy',
        'sklearn',
        'pygad',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\n请运行以下命令安装:")
        print("  python scripts/install_dependencies.py")
        return False
    
    return True


def setup_application():
    """设置应用程序基本属性"""
    # 设置高DPI支持（必须在创建QApplication之前）
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName(AppConfig.APP_NAME)
    app.setApplicationVersion(AppConfig.APP_VERSION)
    app.setOrganizationName(AppConfig.ORGANIZATION)

    # 设置全局字体
    app.setFont(QFont(AppConfig.FONT_FAMILY, AppConfig.FONT_SIZE))

    # 应用全局样式
    apply_global_style(app)

    return app


def show_error_dialog(title, message):
    """显示错误对话框"""
    app = QApplication(sys.argv)
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.exec_()


def main():
    """主函数 - 程序入口点"""
    try:
        # 检查依赖
        if not check_dependencies():
            show_error_dialog(
                "依赖检查失败",
                "缺少必要的依赖包，请先安装所需依赖。\n\n"
                "运行命令: python scripts/install_dependencies.py"
            )
            return 1
        
        # 设置应用程序
        app = setup_application()
        
        # 创建并显示主窗口
        try:
            main_window = MainWindow()
            main_window.show()
            
            print(f"{AppConfig.APP_NAME} v{AppConfig.APP_VERSION} 启动成功")
            print("=" * 50)
            
        except Exception as e:
            show_error_dialog(
                "界面创建失败", 
                f"创建主界面时发生错误:\n{str(e)}\n\n"
                "请检查模块是否正确安装。"
            )
            return 1
        
        # 运行应用程序事件循环
        return app.exec_()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行时发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    # 设置控制台编码为UTF-8（Windows）
    if sys.platform.startswith('win'):
        try:
            import locale
            locale.setlocale(locale.LC_ALL, 'Chinese')
        except:
            pass
    
    # 启动应用程序
    exit_code = main()
    sys.exit(exit_code)
