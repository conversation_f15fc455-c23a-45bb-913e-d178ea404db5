#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仿真界面组件模块

提供批量仿真的用户界面组件。
重构自原来的 batch_simulation_ui.py 文件。

作者: Adams Simulation Team
版本: 2.0.0
"""

import os
from typing import List, Dict, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                             QSpinBox, QDoubleSpinBox, QGroupBox, QHeaderView, 
                             QCheckBox, QMessageBox, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QThread
from PyQt5.QtGui import QFont

# 导入配置和工具
from config import AppConfig, ParameterConfig
from .batch_simulator import BatchSimulator
from utils.data_processor import (generate_parameter_combinations, validate_parameter_ranges,
                                validate_conditions, calculate_relative_errors)
from utils.file_manager import save_to_excel, validate_directory_path

# 直接导入GUI组件
from gui.common_widgets import (ProgressDialog, show_error_message, show_warning_message,
                               show_info_message)
from PyQt5.QtWidgets import QFileDialog


class SimulationWidget(QWidget):
    """仿真界面组件"""
    
    # 信号定义
    simulation_started = pyqtSignal()
    simulation_finished = pyqtSignal()
    simulation_error = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化状态
        self.is_running = False
        self.batch_simulator = None
        self.progress_dialog = None
        
        # 初始化界面
        self._init_ui()
        
        # 设置默认值
        self._setup_default_values()
        
        print("✅ 仿真界面组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 主布局 - 与原来保持一致
        main_layout = QVBoxLayout(self)

        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        scroll.setWidget(content_widget)
        main_layout.addWidget(scroll)

        # 添加各个功能组 - 按原来的顺序和结构
        self._create_path_group(content_layout)
        self._create_parameter_group(content_layout)
        self._create_condition_group(content_layout)
        self._create_sample_group(content_layout)
        self._create_button_group(content_layout)
        self._create_status_group(content_layout)

        # 内容顶部对齐并在底部加弹性伸展以避免大片留白
        content_layout.addStretch(1)
    
    def _create_path_group(self, parent_layout):
        """创建路径设置组"""
        path_group = QGroupBox("结果保存路径")
        path_layout = QHBoxLayout(path_group)

        # 路径输入和浏览按钮
        path_layout.addWidget(QLabel("保存路径:"))
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("请选择结果保存路径...")
        self.path_edit.textChanged.connect(self._on_path_changed)
        path_layout.addWidget(self.path_edit)

        browse_btn = QPushButton("浏览...")
        browse_btn.setFixedWidth(80)
        browse_btn.clicked.connect(self._browse_directory)
        path_layout.addWidget(browse_btn)

        parent_layout.addWidget(path_group)
    
    def _create_parameter_group(self, parent_layout):
        """创建参数设置组"""
        # 批量仿真配置组 - 与原来保持一致
        config_group = QGroupBox("批量仿真配置")
        config_layout = QVBoxLayout()

        # 参数范围设置表格 - 移除"参与采样"列，所有参数都参与采样
        self.param_table = QTableWidget(8, 4)
        self.param_table.setHorizontalHeaderLabels(["参数名称", "默认值", "下限", "上限"])

        # 设置表格的最小高度以确保所有行都能显示
        self.param_table.setMinimumHeight(280)  # 8行 * 35像素/行 = 280像素

        # 设置列宽策略：参数名称列稍宽，其他列平均分配
        header = self.param_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 参数名称列自适应内容
        for i in range(1, 4):
            header.setSectionResizeMode(i, QHeaderView.Stretch)  # 其他列平均分配

        # 调整行高以确保内容完整显示
        self.param_table.verticalHeader().setDefaultSectionSize(30)

        # 设置表格字体大小与界面一致
        self.param_table.setStyleSheet("QTableWidget {font-size:11pt;} QTableWidget::item {font-size:11pt;}")

        # 初始化参数行
        self._setup_parameter_table()

        config_layout.addWidget(self.param_table)
        config_group.setLayout(config_layout)
        parent_layout.addWidget(config_group)
    
    def _create_condition_group(self, parent_layout):
        """创建工况设置组 - 与原来保持一致"""
        # 工况输入表格 - 按照原来的结构
        condition_group = QGroupBox("工况设置")
        condition_layout = QVBoxLayout()
        self.condition_table = QTableWidget(3, 3)  # 增加到3行以显示示例
        self.condition_table.setHorizontalHeaderLabels(["转速(rpm)", "负载扭矩(N·m)", "试验RMS值"])

        # 设置工况表格的最小高度
        self.condition_table.setMinimumHeight(150)  # 增加高度以容纳更多行

        # 设置列宽策略
        condition_header = self.condition_table.horizontalHeader()
        condition_header.setSectionResizeMode(QHeaderView.Stretch)

        # 调整行高
        self.condition_table.verticalHeader().setDefaultSectionSize(30)

        # 设置表格字体大小与界面一致
        self.condition_table.setStyleSheet("QTableWidget {font-size:11pt;} QTableWidget::item {font-size:11pt;}")

        # 添加示例数据
        self._setup_condition_examples()

        # 添加工况表格输入验证
        self.condition_table.itemChanged.connect(self._validate_condition_input)

        condition_layout.addWidget(self.condition_table)
        cond_btn_layout = QHBoxLayout()
        add_cond_btn = QPushButton("添加工况")
        del_cond_btn = QPushButton("删除工况")
        add_cond_btn.clicked.connect(lambda: self.condition_table.insertRow(self.condition_table.rowCount()))
        del_cond_btn.clicked.connect(lambda: self.condition_table.removeRow(self.condition_table.currentRow()) if self.condition_table.rowCount() > 1 else None)
        cond_btn_layout.addWidget(add_cond_btn)
        cond_btn_layout.addWidget(del_cond_btn)
        condition_layout.addLayout(cond_btn_layout)
        condition_group.setLayout(condition_layout)
        parent_layout.addWidget(condition_group)
    
    def _create_sample_group(self, parent_layout):
        """创建采样设置组 - 与原来保持一致"""
        # 样本数和总数显示 - 按照原来的布局
        samples_layout = QHBoxLayout()
        samples_layout.addWidget(QLabel("参数样本数:"))
        self.total_samples_spin = QSpinBox()  # 使用原来的变量名
        self.total_samples_spin.setRange(1, 10000)
        self.total_samples_spin.setValue(20)  # 使用原来的默认值
        samples_layout.addWidget(self.total_samples_spin)
        self.total_sim_label = QLabel()
        samples_layout.addWidget(self.total_sim_label)
        samples_layout.addStretch(1)

        # 联动更新 - 与原来保持一致
        self.total_samples_spin.valueChanged.connect(self.update_total_sim_label)
        self.condition_table.model().rowsInserted.connect(self.update_total_sim_label)
        self.condition_table.model().rowsRemoved.connect(self.update_total_sim_label)
        # 监听表格内容变化
        self.condition_table.itemChanged.connect(self.update_total_sim_label)
        self.update_total_sim_label()

        parent_layout.addLayout(samples_layout)
    
    def _create_button_group(self, parent_layout):
        """创建操作按钮组 - 与原来保持一致"""
        # 操作按钮 - 按照原来的布局
        btn_layout = QHBoxLayout()
        generate_btn = QPushButton("生成参数文件")
        self.run_batch_btn = QPushButton("启动批量仿真")  # 使用原来的变量名
        self.calc_error_btn = QPushButton("计算相对误差")  # 使用原来的变量名

        generate_btn.clicked.connect(self.generate_batch_params_file)  # 使用原来的方法名
        self.run_batch_btn.clicked.connect(self.toggle_batch_simulation)  # 使用原来的方法名
        self.calc_error_btn.clicked.connect(self.calculate_relative_error)  # 使用原来的方法名

        btn_layout.addWidget(generate_btn)
        btn_layout.addWidget(self.run_batch_btn)
        btn_layout.addWidget(self.calc_error_btn)

        parent_layout.addLayout(btn_layout)
    
    def _create_status_group(self, parent_layout):
        """创建状态显示组 - 与原来保持一致"""
        # 进度和状态（移除主界面进度条）- 按照原来的设计
        self.status_label = QLabel("状态：待命")
        parent_layout.addWidget(self.status_label)
    
    def _setup_parameter_table(self):
        """设置参数表格 - 完全按照原来的参数设置"""
        # 使用原来的参数设置方法
        self._setup_param_table_row(0, "s-p刚度(N/mm)", 360000, 400000, decimals=0)
        self._setup_param_table_row(1, "p-r刚度(N/mm)", 740000, 780000, decimals=0)
        self._setup_param_table_row(2, "s-p阻尼(N)", 0, 50, decimals=0)
        self._setup_param_table_row(3, "p-r阻尼(N)", 0, 80, decimals=0)
        self._setup_param_table_row(4, "s-p力指数", 1.5, 3, decimals=2)
        self._setup_param_table_row(5, "p-r力指数", 1.5, 3, decimals=2)
        self._setup_param_table_row(6, "s-p穿透深度(mm)", 0.05, 0.2, decimals=3)
        self._setup_param_table_row(7, "p-r穿透深度(mm)", 0.05, 0.2, decimals=3)

    def _setup_param_table_row(self, row, name, low, high, decimals):
        """设置参数表格行 - 移除参与采样复选框，所有参数都参与采样"""
        self.param_table.setItem(row, 0, QTableWidgetItem(name))
        default_val = (low + high) / 2
        for col, val in [(1, default_val), (2, low), (3, high)]:
            spin_box = QDoubleSpinBox()
            spin_box.setRange(0, 1e9)
            spin_box.setDecimals(decimals)
            spin_box.setValue(val)  # 限制为0或正数
            self.param_table.setCellWidget(row, col, spin_box)
    
    def _setup_default_values(self):
        """设置默认值"""
        # 设置默认保存路径
        default_path = os.path.join(os.getcwd(), "AdamsBatchSimulationResults")
        self.path_edit.setText(default_path)
        
        # 设置示例工况
        self._setup_condition_examples()

        # 更新总仿真数
        self.update_total_sim_label()

    def _browse_directory(self):
        """浏览目录"""
        current_path = self.path_edit.text()
        directory = QFileDialog.getExistingDirectory(
            self, "选择结果保存目录", current_path
        )
        if directory:
            self.path_edit.setText(directory)

    def _setup_condition_examples(self):
        """设置工况表格的示例数据 - 与原来保持一致"""
        # 示例工况数据
        examples = [
            ("1500", "100", "0.85"),    # 第一行示例
            ("2000", "150", "1.20"),    # 第二行示例
            ("2500", "200", "1.55")     # 第三行示例
        ]

        for row, (speed, torque, rms) in enumerate(examples):
            self.condition_table.setItem(row, 0, QTableWidgetItem(speed))
            self.condition_table.setItem(row, 1, QTableWidgetItem(torque))
            self.condition_table.setItem(row, 2, QTableWidgetItem(rms))
    
    def _on_path_changed(self, path: str):
        """路径改变事件"""
        if path and validate_directory_path(path, create_if_not_exist=True):
            self.status_label.setText(f"状态：路径已设置 - {os.path.basename(path)}")
        else:
            self.status_label.setText("状态：路径无效")
    
    def _validate_condition_input(self, item):
        """验证工况输入"""
        if not item or not item.text().strip():
            return
        
        try:
            value = float(item.text().strip())
            if value < 0:
                row = item.row() + 1
                col_names = ["转速", "负载扭矩", "试验RMS值"]
                col_name = col_names[item.column()]
                
                show_warning_message(
                    self, "输入错误",
                    f"第 {row} 行 {col_name} 不能为负数！\n请输入0或正数。"
                )
                
                # 清空错误输入
                self.condition_table.itemChanged.disconnect(self._validate_condition_input)
                item.setText("")
                self.condition_table.itemChanged.connect(self._validate_condition_input)
                
        except ValueError:
            row = item.row() + 1
            col_names = ["转速", "负载扭矩", "试验RMS值"]
            col_name = col_names[item.column()]
            
            show_warning_message(
                self, "输入错误",
                f"第 {row} 行 {col_name} 必须是数字！\n请输入有效的数值。"
            )
            
            # 清空错误输入
            self.condition_table.itemChanged.disconnect(self._validate_condition_input)
            item.setText("")
            self.condition_table.itemChanged.connect(self._validate_condition_input)
        
        # 更新总仿真数
        self.update_total_sim_label()

    def _add_condition_row(self):
        """添加工况行"""
        self.condition_table.insertRow(self.condition_table.rowCount())
        self.update_total_sim_label()

    def _delete_condition_row(self):
        """删除工况行"""
        current_row = self.condition_table.currentRow()
        if current_row >= 0 and self.condition_table.rowCount() > 1:
            self.condition_table.removeRow(current_row)
            self.update_total_sim_label()
    
    def update_total_sim_label(self):
        """更新总仿真数显示 - 与原来保持一致"""
        n = self.total_samples_spin.value()
        # 只计算完整填写的工况数量
        m = self._count_complete_conditions()
        self.total_sim_label.setText(f"  (实际仿真总数: {n*m})")

    def _count_complete_conditions(self):
        """计算完整填写的工况数量 - 与原来保持一致"""
        count = 0
        for row in range(self.condition_table.rowCount()):
            # 检查该行是否所有列都有有效数据
            complete = True
            for col in range(3):  # 3列：转速、扭矩、RMS
                item = self.condition_table.item(row, col)
                if not item or not item.text().strip():
                    complete = False
                    break
                try:
                    value = float(item.text().strip())
                    if value < 0:  # 不允许负数
                        complete = False
                        break
                except ValueError:
                    complete = False
                    break
            if complete:
                count += 1
        return count
    
    # 删除重复的方法，使用 _count_complete_conditions 代替
    
    def generate_batch_params_file(self):
        """生成参数文件 - 使用原来的方法名"""
        try:
            # 验证保存路径
            save_path = self.path_edit.text().strip()
            if not save_path or not validate_directory_path(save_path, create_if_not_exist=True):
                show_error_message(self, "路径错误", "请设置有效的保存路径")
                return
            
            # 获取参数配置
            param_ranges = self._get_parameter_ranges()
            
            # 验证参数
            param_errors = validate_parameter_ranges(param_ranges)
            if param_errors:
                show_error_message(self, "参数错误", "\n".join(param_errors))
                return
            
            # 获取工况数据
            conditions = self._get_conditions()
            
            # 验证工况
            condition_errors = validate_conditions(conditions)
            if condition_errors:
                show_error_message(self, "工况错误", "\n".join(condition_errors))
                return
            
            # 生成参数组合
            sample_count = self.total_samples_spin.value()  # 使用原来的变量名
            df = generate_parameter_combinations(param_ranges, conditions, sample_count)
            
            # 保存文件
            param_file = os.path.join(save_path, AppConfig.BATCH_PARAMS_FILE)
            if save_to_excel(df, param_file):
                show_info_message(
                    self, "生成成功",
                    f"参数文件已生成: {AppConfig.BATCH_PARAMS_FILE}\n"
                    f"总任务数: {len(df)}"
                )
                self.status_label.setText(f"状态：参数文件已生成 ({len(df)} 个任务)")
            else:
                show_error_message(self, "保存失败", "无法保存参数文件")

        except Exception as e:
            show_error_message(self, "生成失败", f"生成参数文件时发生错误:\n{str(e)}")
            self.status_label.setText("状态：参数文件生成失败")
    
    def _get_parameter_ranges(self) -> Dict:
        """获取参数范围配置 - 所有参数都参与采样"""
        param_ranges = {}
        param_names = list(ParameterConfig.PARAM_NAMES.keys())

        for row in range(self.param_table.rowCount()):
            if row < len(param_names):
                param_name = param_names[row]

                # 获取数值
                default_widget = self.param_table.cellWidget(row, 1)
                min_widget = self.param_table.cellWidget(row, 2)
                max_widget = self.param_table.cellWidget(row, 3)

                param_ranges[param_name] = {
                    "default": default_widget.value(),
                    "min": min_widget.value(),
                    "max": max_widget.value(),
                    "enabled": True  # 所有参数都参与采样
                }
        
        return param_ranges
    
    def _get_conditions(self) -> List[Dict]:
        """获取工况数据"""
        conditions = []
        
        for row in range(self.condition_table.rowCount()):
            if self._is_condition_complete(row):
                speed_item = self.condition_table.item(row, 0)
                torque_item = self.condition_table.item(row, 1)
                rms_item = self.condition_table.item(row, 2)
                
                conditions.append({
                    "speed": float(speed_item.text().strip()),
                    "torque": float(torque_item.text().strip()),
                    "test_rms": float(rms_item.text().strip())
                })
        
        return conditions
    
    def toggle_batch_simulation(self):
        """切换仿真状态 - 使用原来的方法名"""
        if self.is_running:
            self._stop_simulation()
        else:
            self._start_simulation()
    
    def _start_simulation(self):
        """启动仿真"""
        try:
            save_path = self.path_edit.text().strip()
            if not save_path:
                show_error_message(self, "路径错误", "请设置保存路径")
                return
            
            # 检查参数文件
            param_file = os.path.join(save_path, AppConfig.BATCH_PARAMS_FILE)
            if not os.path.exists(param_file):
                show_error_message(self, "文件缺失", "请先生成参数文件")
                return
            
            # 创建仿真器
            self.batch_simulator = BatchSimulator()
            self.batch_simulator.progress_updated.connect(self._on_progress_updated)
            self.batch_simulator.simulation_finished.connect(self._on_simulation_finished)
            self.batch_simulator.simulation_error.connect(self._on_simulation_error)
            
            # 显示进度对话框
            self.progress_dialog = ProgressDialog("批量仿真进行中", self)
            self.progress_dialog.cancelled.connect(self._stop_simulation)
            self.progress_dialog.show()
            
            # 启动仿真
            self.batch_simulator.start_simulation(save_path)
            
            # 更新界面状态
            self.is_running = True
            self.run_batch_btn.setText("暂停仿真")  # 使用原来的按钮文本
            self.status_label.setText("状态：批量仿真进行中...")
            
            self.simulation_started.emit()
            
        except Exception as e:
            show_error_message(self, "启动失败", f"启动仿真时发生错误:\n{str(e)}")
    
    def _stop_simulation(self):
        """停止仿真"""
        if self.batch_simulator:
            self.batch_simulator.stop_simulation()
        
        self._reset_simulation_state()
    
    def _reset_simulation_state(self):
        """重置仿真状态"""
        self.is_running = False
        self.run_batch_btn.setText("启动批量仿真")
        
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self.batch_simulator = None
    
    def _on_progress_updated(self, progress: int, status: str):
        """进度更新事件"""
        if self.progress_dialog:
            self.progress_dialog.update_progress(progress, status)
        
        self.status_label.setText(f"状态：{status}")
    
    def _on_simulation_finished(self):
        """仿真完成事件"""
        self._reset_simulation_state()
        self.status_label.setText("状态：批量仿真完成")
        show_info_message(self, "仿真完成", "批量仿真已完成，可以计算相对误差")
        self.simulation_finished.emit()

    def _on_simulation_error(self, error_msg: str):
        """仿真错误事件"""
        self._reset_simulation_state()
        self.status_label.setText("状态：仿真失败")
        show_error_message(self, "仿真失败", f"仿真过程中发生错误:\n{error_msg}")
        self.simulation_error.emit(error_msg)
    
    def calculate_relative_error(self):
        """计算相对误差 - 使用原来的方法名"""
        try:
            save_path = self.path_edit.text().strip()
            if not save_path:
                show_error_message(self, "路径错误", "请设置保存路径")
                return
            
            param_file = os.path.join(save_path, AppConfig.BATCH_PARAMS_FILE)
            if not os.path.exists(param_file):
                show_error_message(self, "文件缺失", "请先生成参数文件并完成仿真")
                return
            
            # 计算误差并保存
            from .batch_simulator import calculate_error_summary
            
            summary_file = os.path.join(save_path, AppConfig.ERROR_SUMMARY_FILE)
            total_groups, valid_groups, filtered_groups = calculate_error_summary(
                param_file, summary_file
            )
            
            show_info_message(
                self, "计算完成",
                f"相对误差计算完成！\n"
                f"总组数: {total_groups}\n"
                f"有效组数: {valid_groups}\n"
                f"结果已保存到: {AppConfig.ERROR_SUMMARY_FILE}"
            )
            
            self.status_label.setText("状态：相对误差计算完成")

        except Exception as e:
            show_error_message(self, "计算失败", f"计算相对误差时发生错误:\n{str(e)}")
            self.status_label.setText("状态：误差计算失败")
    
    def is_simulation_running(self) -> bool:
        """检查仿真是否正在运行"""
        return self.is_running
    
    def stop_simulation(self):
        """外部调用停止仿真"""
        self._stop_simulation()
    
    def cleanup(self):
        """清理资源"""
        if self.batch_simulator:
            self.batch_simulator.stop_simulation()
        
        if self.progress_dialog:
            self.progress_dialog.close()


# 导出的公共接口
__all__ = ['SimulationWidget']
