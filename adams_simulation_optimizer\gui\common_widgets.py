#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用界面组件模块

提供可复用的界面组件，如进度对话框、文件选择器等。

作者: Adams Simulation Team
版本: 2.0.0
"""

import os
from typing import Optional, List, Callable
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QProgressBar, QTextEdit, QFileDialog,
                             QGroupBox, QTableWidget, QTableWidgetItem, 
                             QHeaderView, QMessageBox, QWidget, QLineEdit,
                             QSpinBox, QDoubleSpinBox, QCheckBox, QComboBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from config import StyleConfig, AppConfig


class ProgressDialog(QDialog):
    """进度对话框"""
    
    # 信号定义
    cancelled = pyqtSignal()
    
    def __init__(self, title: str = "处理中", parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 200)
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint)
        
        self._init_ui()
        
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 状态标签
        self.status_label = QLabel("准备中...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet(f"""
            font-size: 14px;
            font-weight: bold;
            color: {StyleConfig.TEXT_PRIMARY};
            padding: 10px;
        """)
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {StyleConfig.BORDER_COLOR};
                border-radius: 4px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }}
            QProgressBar::chunk {{
                background-color: {StyleConfig.PRIMARY_COLOR};
                border-radius: 3px;
            }}
        """)
        layout.addWidget(self.progress_bar)
        
        # 详细信息
        self.detail_label = QLabel("")
        self.detail_label.setAlignment(Qt.AlignCenter)
        self.detail_label.setStyleSheet(f"""
            color: {StyleConfig.TEXT_SECONDARY};
            font-size: 12px;
            padding: 5px;
        """)
        layout.addWidget(self.detail_label)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self._on_cancel)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
    
    def update_progress(self, value: int, status: str = "", detail: str = ""):
        """更新进度"""
        self.progress_bar.setValue(value)
        
        if status:
            self.status_label.setText(status)
        
        if detail:
            self.detail_label.setText(detail)
    
    def set_indeterminate(self, indeterminate: bool = True):
        """设置为不确定进度模式"""
        if indeterminate:
            self.progress_bar.setRange(0, 0)
        else:
            self.progress_bar.setRange(0, 100)
    
    def _on_cancel(self):
        """取消按钮点击事件"""
        self.cancelled.emit()
        self.reject()


class FileSelector(QWidget):
    """文件选择器组件"""
    
    # 信号定义
    file_selected = pyqtSignal(str)
    
    def __init__(self, label_text: str = "文件路径:", 
                 file_filter: str = "所有文件 (*.*)",
                 mode: str = "open",  # "open", "save", "directory"
                 parent=None):
        super().__init__(parent)
        self.file_filter = file_filter
        self.mode = mode
        
        self._init_ui(label_text)
    
    def _init_ui(self, label_text: str):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 标签
        if label_text:
            label = QLabel(label_text)
            label.setMinimumWidth(80)
            layout.addWidget(label)
        
        # 路径输入框
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("请选择文件路径...")
        self.path_edit.textChanged.connect(self._on_path_changed)
        layout.addWidget(self.path_edit)
        
        # 浏览按钮
        self.browse_button = QPushButton("浏览...")
        self.browse_button.setFixedWidth(80)
        self.browse_button.clicked.connect(self._browse_file)
        layout.addWidget(self.browse_button)
    
    def _browse_file(self):
        """浏览文件"""
        current_path = self.path_edit.text()
        
        if self.mode == "open":
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择文件", current_path, self.file_filter
            )
        elif self.mode == "save":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存文件", current_path, self.file_filter
            )
        elif self.mode == "directory":
            file_path = QFileDialog.getExistingDirectory(
                self, "选择目录", current_path
            )
        else:
            return
        
        if file_path:
            self.path_edit.setText(file_path)
    
    def _on_path_changed(self, path: str):
        """路径改变事件"""
        self.file_selected.emit(path)
    
    def get_path(self) -> str:
        """获取路径"""
        return self.path_edit.text()
    
    def set_path(self, path: str):
        """设置路径"""
        self.path_edit.setText(path)


class ParameterTable(QTableWidget):
    """参数表格组件"""
    
    # 信号定义
    parameter_changed = pyqtSignal(int, str, object)  # 行号, 列名, 值
    
    def __init__(self, headers: List[str], parent=None):
        super().__init__(parent)
        self.headers = headers
        
        self._init_table()
        self._setup_signals()
    
    def _init_table(self):
        """初始化表格"""
        self.setColumnCount(len(self.headers))
        self.setHorizontalHeaderLabels(self.headers)
        
        # 设置表格属性
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        
        # 设置列宽
        header = self.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(self.headers) - 1):
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)
    
    def _setup_signals(self):
        """设置信号连接"""
        self.cellChanged.connect(self._on_cell_changed)
    
    def _on_cell_changed(self, row: int, column: int):
        """单元格改变事件"""
        if column < len(self.headers):
            column_name = self.headers[column]
            item = self.item(row, column)
            value = item.text() if item else ""
            self.parameter_changed.emit(row, column_name, value)
    
    def add_parameter_row(self, values: List = None):
        """添加参数行"""
        row = self.rowCount()
        self.insertRow(row)
        
        if values:
            for col, value in enumerate(values):
                if col < self.columnCount():
                    item = QTableWidgetItem(str(value))
                    self.setItem(row, col, item)
        
        return row
    
    def get_parameter_data(self) -> List[dict]:
        """获取参数数据"""
        data = []
        for row in range(self.rowCount()):
            row_data = {}
            for col in range(self.columnCount()):
                header = self.headers[col]
                item = self.item(row, col)
                value = item.text() if item else ""
                row_data[header] = value
            data.append(row_data)
        return data
    
    def set_parameter_data(self, data: List[dict]):
        """设置参数数据"""
        self.setRowCount(0)
        
        for row_data in data:
            row = self.add_parameter_row()
            for col, header in enumerate(self.headers):
                if header in row_data:
                    item = QTableWidgetItem(str(row_data[header]))
                    self.setItem(row, col, item)


class StatusWidget(QWidget):
    """状态显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._init_ui()
    
    def _init_ui(self):
        """初始化界面"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet(f"""
            color: {StyleConfig.TEXT_PRIMARY};
            font-weight: bold;
            padding: 2px 8px;
        """)
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 详细信息标签
        self.detail_label = QLabel("")
        self.detail_label.setStyleSheet(f"""
            color: {StyleConfig.TEXT_SECONDARY};
            font-size: 11px;
            padding: 2px 8px;
        """)
        layout.addWidget(self.detail_label)
    
    def set_status(self, status: str, detail: str = ""):
        """设置状态"""
        self.status_label.setText(status)
        self.detail_label.setText(detail)
    
    def set_success(self, message: str):
        """设置成功状态"""
        self.status_label.setText(f"✅ {message}")
        self.status_label.setStyleSheet(f"""
            color: {StyleConfig.SUCCESS_COLOR};
            font-weight: bold;
            padding: 2px 8px;
        """)
    
    def set_error(self, message: str):
        """设置错误状态"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet(f"""
            color: {StyleConfig.ERROR_COLOR};
            font-weight: bold;
            padding: 2px 8px;
        """)
    
    def set_warning(self, message: str):
        """设置警告状态"""
        self.status_label.setText(f"⚠️ {message}")
        self.status_label.setStyleSheet(f"""
            color: {StyleConfig.WARNING_COLOR};
            font-weight: bold;
            padding: 2px 8px;
        """)


def show_info_message(parent, title: str, message: str):
    """显示信息消息框"""
    QMessageBox.information(parent, title, message)


def show_warning_message(parent, title: str, message: str):
    """显示警告消息框"""
    QMessageBox.warning(parent, title, message)


def show_error_message(parent, title: str, message: str):
    """显示错误消息框"""
    QMessageBox.critical(parent, title, message)


def show_question_message(parent, title: str, message: str) -> bool:
    """显示询问消息框"""
    reply = QMessageBox.question(
        parent, title, message,
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.No
    )
    return reply == QMessageBox.Yes


# 导出的公共接口
__all__ = [
    'ProgressDialog',
    'FileSelector', 
    'ParameterTable',
    'StatusWidget',
    'show_info_message',
    'show_warning_message',
    'show_error_message',
    'show_question_message'
]
