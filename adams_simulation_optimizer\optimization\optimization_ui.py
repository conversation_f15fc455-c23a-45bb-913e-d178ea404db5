#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化界面组件模块

提供参数优化的用户界面组件。
重构自原来的 optimization_ui.py 文件。

作者: Adams Simulation Team
版本: 2.0.0
"""

import os
from typing import Dict, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                             QSpinBox, QDoubleSpinBox, QGroupBox, QHeaderView, 
                             QComboBox, QFormLayout, QScrollArea, QSplitter,
                             QFrame)
from PyQt5.QtCore import Qt, pyqtSignal, QThread
from PyQt5.QtGui import QFont

# 导入配置和工具
from config import AppConfig, MLConfig, ParameterConfig
from gui.common_widgets import (ProgressDialog, show_error_message, show_info_message)
from PyQt5.QtWidgets import Q<PERSON>ileDialog, QSizePolicy
from .ml_optimizer import <PERSON><PERSON><PERSON>ptim<PERSON>
from utils.file_manager import validate_file_path


class OptimizationWorker(QThread):
    """优化工作线程"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)
    optimization_finished = pyqtSignal(dict)
    optimization_error = pyqtSignal(str)
    
    def __init__(self, xlsx_path: str, test_ratio: float, generations: int, 
                 pop_size: int, model_name: str, parent=None):
        super().__init__(parent)
        self.xlsx_path = xlsx_path
        self.test_ratio = test_ratio
        self.generations = generations
        self.pop_size = pop_size
        self.model_name = model_name
        self.optimizer = None
    
    def run(self):
        """运行优化"""
        try:
            self.optimizer = MLOptimizer()
            
            # 连接信号
            self.optimizer.progress_updated.connect(self.progress_updated)
            self.optimizer.optimization_finished.connect(self.optimization_finished)
            self.optimizer.optimization_error.connect(self.optimization_error)
            
            # 执行优化
            self.optimizer.optimize(
                self.xlsx_path,
                test_ratio=self.test_ratio,
                generations=self.generations,
                pop_size=self.pop_size,
                model_name=self.model_name
            )
            
        except Exception as e:
            self.optimization_error.emit(str(e))
    
    def stop_optimization(self):
        """停止优化"""
        if self.optimizer:
            self.optimizer.stop_optimization()


class OptimizationWidget(QWidget):
    """优化界面组件"""
    
    # 信号定义
    optimization_started = pyqtSignal()
    optimization_finished = pyqtSignal(dict)
    optimization_error = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化状态
        self.is_running = False
        self.worker = None
        self.progress_dialog = None
        self.optimization_result = None
        
        # 初始化界面
        self._init_ui()
        
        # 设置默认值
        self._setup_default_values()
        
        print("✅ 优化界面组件初始化完成")
    
    def _init_ui(self):
        """初始化用户界面 - 与原来的左右分割布局保持一致"""
        main_layout = QHBoxLayout(self)

        # 创建左右分割器 - 与原来保持一致
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # ----- 左侧参数设置区 -----
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(10, 10, 10, 10)

        # 添加左侧功能组
        self._create_file_group(left_layout)
        self._create_algorithm_group(left_layout)
        self._create_button_group(left_layout)
        self._create_info_group(left_layout)

        # 添加弹性空间
        left_layout.addStretch(1)

        # ----- 右侧结果显示区 -----
        right_widget = QScrollArea()
        right_widget.setWidgetResizable(True)
        right_content = QWidget()
        right_layout = QVBoxLayout(right_content)
        right_layout.setContentsMargins(10, 10, 10, 10)

        # 添加右侧功能组
        self._create_result_group(right_layout)

        # 将右侧内容添加到滚动区域
        right_widget.setWidget(right_content)

        # 添加左右两侧到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([300, 700])  # 设置初始分割比例
    
    def _create_file_group(self, parent_layout):
        """创建文件设置组 - 与原来保持一致"""
        # ----- 文件选择 -----
        # 添加标题标签
        file_title = QLabel("仿真文件设置")
        file_title.setStyleSheet("color: #0078D4; font-weight: bold;")
        parent_layout.addWidget(file_title)

        # 添加无标题的分组框
        file_group = self._create_group_box("")
        flayout = QHBoxLayout()
        flayout.addWidget(QLabel("仿真结果文件:"))
        self.file_edit = QLineEdit()
        self.file_edit.setPlaceholderText("请选择 error_summary.xlsx 文件")
        flayout.addWidget(self.file_edit, 1)
        browse_btn = QPushButton("浏览")
        browse_btn.clicked.connect(self.browse_file)
        flayout.addWidget(browse_btn)
        flayout.setContentsMargins(8, 4, 8, 4)
        flayout.setSpacing(6)
        file_group.setLayout(flayout)
        file_group.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)
        parent_layout.addWidget(file_group)
    
    def _create_algorithm_group(self, parent_layout):
        """创建算法配置组 - 与原来保持一致"""
        # ----- 优化算法配置 -----
        # 添加标题标签
        ga_title = QLabel("优化算法配置")
        ga_title.setStyleSheet("color: #0078D4; font-weight: bold;")
        parent_layout.addWidget(ga_title)

        # 添加无标题的分组框
        ga_group = self._create_group_box("")
        glayout = QFormLayout()

        # 拟合模型选择
        self.model_combo = QComboBox()
        self.model_combo.addItems(list(MLConfig.MODEL_NAMES.keys()))
        self.model_combo.setCurrentText("随机森林")
        glayout.addRow("拟合模型:", self.model_combo)

        # 测试集比例
        self.test_ratio_spin = QDoubleSpinBox()
        self.test_ratio_spin.setRange(0.01, 0.99)
        self.test_ratio_spin.setSingleStep(0.05)
        self.test_ratio_spin.setDecimals(2)
        self.test_ratio_spin.setValue(AppConfig.DEFAULT_TEST_RATIO)
        glayout.addRow("测试集比例:", self.test_ratio_spin)

        # 迭代次数
        self.generations_spin = QSpinBox()
        self.generations_spin.setRange(1, 10000)
        self.generations_spin.setValue(AppConfig.DEFAULT_GENERATIONS)
        glayout.addRow("迭代次数:", self.generations_spin)

        # 种群规模
        self.pop_size_spin = QSpinBox()
        self.pop_size_spin.setRange(1, 1000)
        self.pop_size_spin.setValue(AppConfig.DEFAULT_POPULATION_SIZE)
        glayout.addRow("种群规模:", self.pop_size_spin)

        glayout.setContentsMargins(8, 4, 8, 4)
        glayout.setSpacing(6)

        ga_group.setLayout(glayout)
        ga_group.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)
        parent_layout.addWidget(ga_group)
    
    def _create_button_group(self, parent_layout):
        """创建操作按钮组 - 与原来保持一致"""
        # ----- 操作按钮 -----
        btn_layout = QHBoxLayout()
        self.run_btn = QPushButton("开始优化")  # 使用原来的变量名
        self.run_btn.clicked.connect(self.start_optimize)  # 使用原来的方法名
        btn_layout.addStretch(1)
        btn_layout.addWidget(self.run_btn)
        parent_layout.addLayout(btn_layout)

    def _create_info_group(self, parent_layout):
        """创建参数说明组 - 调整字体大小和位置"""
        # 添加更多间距，让说明往下移
        parent_layout.addSpacing(30)

        # ----- 参数说明（左侧底部） -----
        info_text = (
            "<b>表格参数说明</b><br>"
            "s-p: 太阳轮与行星轮接触参数<br>"
            "r-p: 内齿圈与行星轮接触参数"
        )
        info_label = QLabel(info_text)
        # 设置字体大小为12pt，与界面整体风格保持一致
        info_label.setStyleSheet("font-size:12pt; color:#333333; line-height:1.5; padding:5px;")
        info_label.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        info_label.setTextFormat(Qt.RichText)
        parent_layout.addWidget(info_label)

    def _create_group_box(self, title=""):
        """创建带蓝色标题的分组框，但不使用 QGroupBox 的标题 - 与原来保持一致"""
        # 创建无标题的 QGroupBox
        group = QGroupBox()
        group.setStyleSheet("""
            QGroupBox {
                border: 1px solid #0078D4;
                border-radius: 4px;
                margin-top: 0px;
            }
        """)
        return group
    
    def _create_result_group(self, parent_layout):
        """创建结果显示组 - 与原来保持一致"""
        # ----- 优化结果区域 -----
        # 添加标题标签
        result_title = QLabel("参数优化结果")
        result_title.setStyleSheet("color: #0078D4; font-weight: bold;")
        parent_layout.addWidget(result_title)

        # 添加无标题的分组框
        result_group = self._create_group_box("")
        result_layout = QVBoxLayout()

        # 结果表格 - 与原来保持一致
        self.result_table = QTableWidget(0, 2)
        self.result_table.setHorizontalHeaderLabels(["仿真参数", "最优值"])
        self.result_table.horizontalHeader().setDefaultAlignment(Qt.AlignCenter)
        self.result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.result_table.verticalHeader().setVisible(False)
        self.result_table.setAlternatingRowColors(True)
        self.result_table.setShowGrid(False)
        self.result_table.setStyleSheet(
            "QHeaderView::section {background-color:#0078D4; color:#FFFFFF; font-weight:bold; font-size:11pt;}\n"
            "QTableWidget {alternate-background-color:#FAF9F8; font-size:11pt;}\n"
            "QTableWidget::item {font-size:11pt;}"
        )
        result_layout.addWidget(self.result_table)

        result_group.setLayout(result_layout)
        parent_layout.addWidget(result_group)
    
    # 状态显示组已移除，原来的界面没有状态栏
    
    def _setup_result_table(self):
        """设置结果表格 - 初始为空，等待优化结果"""
        # 初始表格为空，等待优化结果填充
        pass
    
    def _setup_default_values(self):
        """设置默认值"""
        # 设置默认文件路径提示
        pass  # 不需要额外设置

    def browse_file(self):
        """浏览文件 - 与原来保持一致"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择仿真结果文件", "",
            "Excel文件 (*.xlsx);;所有文件 (*.*)"
        )
        if file_path:
            self.file_edit.setText(file_path)
    
    def start_optimize(self):
        """开始优化 - 使用原来的方法名"""
        self._start_optimization()
    
    def _toggle_optimization(self):
        """切换优化状态"""
        if self.is_running:
            self._stop_optimization()
        else:
            self._start_optimization()
    
    def _start_optimization(self):
        """启动优化"""
        try:
            # 验证输入
            file_path = self.file_edit.text().strip()  # 使用原来的文件输入框
            if not file_path or not validate_file_path(file_path, must_exist=True, extension='.xlsx'):
                show_error_message(self, "文件错误", "请选择有效的Excel文件")
                return
            
            # 获取参数
            model_name = self.model_combo.currentText()
            test_ratio = self.test_ratio_spin.value()
            generations = self.generations_spin.value()
            pop_size = self.pop_size_spin.value()
            
            # 创建工作线程
            self.worker = OptimizationWorker(
                file_path, test_ratio, generations, pop_size, model_name
            )
            
            # 连接信号
            self.worker.progress_updated.connect(self._on_progress_updated)
            self.worker.optimization_finished.connect(self._on_optimization_finished)
            self.worker.optimization_error.connect(self._on_optimization_error)
            
            # 显示进度对话框
            self.progress_dialog = ProgressDialog("参数优化进行中", self)
            self.progress_dialog.cancelled.connect(self._stop_optimization)
            self.progress_dialog.show()
            
            # 启动优化
            self.worker.start()
            
            # 更新界面状态
            self.is_running = True
            self.run_btn.setText("停止优化")  # 使用原来的按钮
            
            self.optimization_started.emit()
            
        except Exception as e:
            show_error_message(self, "启动失败", f"启动优化时发生错误:\n{str(e)}")
    
    def _stop_optimization(self):
        """停止优化"""
        if self.worker:
            self.worker.stop_optimization()
            self.worker.wait(3000)  # 等待3秒
            if self.worker.isRunning():
                self.worker.terminate()
        
        self._reset_optimization_state()
    
    def _reset_optimization_state(self):
        """重置优化状态"""
        self.is_running = False
        self.run_btn.setText("开始优化")
        
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None
        
        self.worker = None
    
    def _on_progress_updated(self, progress: int, status: str):
        """进度更新事件"""
        if self.progress_dialog:
            self.progress_dialog.update_progress(progress, status)
        
        # 不需要状态显示，原来的界面没有状态栏
    
    def _on_optimization_finished(self, result: Dict):
        """优化完成事件"""
        self._reset_optimization_state()
        self.optimization_result = result
        
        # 显示结果
        self._display_optimization_result(result)
        
        # 不需要状态显示
        
        show_info_message(
            self, "优化完成", 
            f"参数优化已完成！\n"
            f"模型性能 R²: {result['r2']:.4f}\n"
            f"预测误差: {result['best_pred_error']:.4f}"
        )
        
        self.optimization_finished.emit(result)
    
    def _on_optimization_error(self, error_msg: str):
        """优化错误事件"""
        self._reset_optimization_state()
        show_error_message(self, "优化失败", f"优化过程中发生错误:\n{error_msg}")
        self.optimization_error.emit(error_msg)
    
    def _display_optimization_result(self, result: Dict):
        """显示优化结果 - 与原来保持一致"""
        try:
            param_names = result['param_names']
            best_solution = result['best_solution']

            # 参数名称映射 - 使用原来的映射
            _PARAM_DISPLAY = {
                "sun_stiff": "接触刚度（s-p） (N/mm)",
                "ring_stiff": "接触刚度（r-p） (N/mm)",
                "sun_damping": "接触阻尼（s-p） (N·s/mm)",
                "ring_damping": "接触阻尼（r-p） (N·s/mm)",
                "sun_exponent": "力指数（s-p）",
                "ring_exponent": "力指数（r-p）",
                "sun_dmax": "穿透深度（s-p） (mm)",
                "ring_dmax": "穿透深度（r-p） (mm)",
            }

            # 清空并重新设置表格
            self.result_table.setRowCount(len(param_names))

            # 填充结果表格
            for row, param_code in enumerate(param_names):
                param_value = best_solution[row]

                # 参数名称
                param_display = _PARAM_DISPLAY.get(param_code, param_code)
                name_item = QTableWidgetItem(param_display)
                name_item.setFlags(name_item.flags() & ~Qt.ItemIsEditable)
                self.result_table.setItem(row, 0, name_item)

                # 格式化数值（6位有效数字）
                formatted_value = f"{param_value:.6g}"
                value_item = QTableWidgetItem(formatted_value)
                value_item.setFlags(value_item.flags() & ~Qt.ItemIsEditable)
                self.result_table.setItem(row, 1, value_item)

        except Exception as e:
            show_error_message(self, "显示错误", f"显示优化结果时发生错误:\n{str(e)}")
    
    def get_optimization_result(self) -> Optional[Dict]:
        """获取优化结果"""
        return self.optimization_result
    
    def is_optimization_running(self) -> bool:
        """检查优化是否正在运行"""
        return self.is_running
    
    def stop_optimization(self):
        """外部调用停止优化"""
        self._stop_optimization()
    
    def cleanup(self):
        """清理资源"""
        if self.worker:
            self.worker.stop_optimization()
            self.worker.wait(1000)
            if self.worker.isRunning():
                self.worker.terminate()
        
        if self.progress_dialog:
            self.progress_dialog.close()


# 导出的公共接口
__all__ = ['OptimizationWidget']
