#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无GUI测试脚本 - 验证重构后的核心功能
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_core_functionality():
    """测试核心功能"""
    print("=" * 50)
    print("Adams 仿真与优化工具 - 核心功能测试")
    print("=" * 50)
    
    tests = []
    
    # 1. 测试配置模块
    try:
        from config import AppConfig, StyleConfig, ParameterConfig, MLConfig
        print("✅ 配置模块导入成功")
        print(f"   应用名称: {AppConfig.APP_NAME}")
        print(f"   应用版本: {AppConfig.APP_VERSION}")
        tests.append(("配置模块", True))
    except Exception as e:
        print(f"❌ 配置模块测试失败: {e}")
        tests.append(("配置模块", False))
    
    # 2. 测试工具模块
    try:
        from utils.adams_interface import send_adams_command, process_simulation_results
        from utils.data_processor import generate_parameter_combinations, calculate_relative_errors
        from utils.file_manager import save_to_excel, load_from_excel
        print("✅ 工具模块导入成功")
        tests.append(("工具模块", True))
    except Exception as e:
        print(f"❌ 工具模块测试失败: {e}")
        tests.append(("工具模块", False))
    
    # 3. 测试仿真核心
    try:
        from simulation.batch_simulator import BatchSimulator, run_batch_simulation
        print("✅ 仿真核心模块导入成功")
        tests.append(("仿真核心", True))
    except Exception as e:
        print(f"❌ 仿真核心测试失败: {e}")
        tests.append(("仿真核心", False))
    
    # 4. 测试优化核心
    try:
        from optimization.ml_optimizer import MLOptimizer, optimize_parameters
        print("✅ 优化核心模块导入成功")
        tests.append(("优化核心", True))
    except Exception as e:
        print(f"❌ 优化核心测试失败: {e}")
        tests.append(("优化核心", False))
    
    # 5. 测试界面组件（不创建实例）
    try:
        from simulation.simulation_ui import SimulationWidget
        from optimization.optimization_ui import OptimizationWidget
        print("✅ 界面组件导入成功")
        tests.append(("界面组件", True))
    except Exception as e:
        print(f"❌ 界面组件测试失败: {e}")
        tests.append(("界面组件", False))
    
    # 6. 测试文件结构
    try:
        required_files = [
            "main.py", "config.py", "requirements.txt",
            "utils/__init__.py", "utils/adams_interface.py",
            "simulation/__init__.py", "simulation/batch_simulator.py",
            "optimization/__init__.py", "optimization/ml_optimizer.py",
            "scripts/install_dependencies.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ 缺少文件: {missing_files}")
            tests.append(("文件结构", False))
        else:
            print("✅ 文件结构完整")
            tests.append(("文件结构", True))
            
    except Exception as e:
        print(f"❌ 文件结构测试失败: {e}")
        tests.append(("文件结构", False))
    
    # 统计结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = sum(1 for _, result in tests if result)
    total = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} {status}")
    
    print(f"\n总测试数: {total}")
    print(f"通过数量: {passed}")
    print(f"失败数量: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有核心功能测试通过！")
        print("重构成功，界面布局已修正！")
        return 0
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败")
        return 1

if __name__ == "__main__":
    try:
        exit_code = test_core_functionality()
        sys.exit(exit_code)
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
