#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查和安装脚本
检查并安装运行整合界面所需的依赖包
"""

import sys
import subprocess
import importlib

def check_and_install_package(package_name, import_name=None, pip_name=None):
    """
    检查并安装包
    
    Args:
        package_name: 显示名称
        import_name: 导入时使用的名称
        pip_name: pip安装时使用的名称
    """
    if import_name is None:
        import_name = package_name.lower()
    if pip_name is None:
        pip_name = package_name.lower()
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"✗ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("=== 依赖检查和安装 ===")
    print("正在检查运行整合界面所需的依赖包...")
    print()
    
    # 必需的依赖包
    dependencies = [
        ("PyQt5", "PyQt5", "PyQt5"),
        ("NumPy", "numpy", "numpy"),
        ("Pandas", "pandas", "pandas"),
        ("SciPy", "scipy", "scipy"),
        ("Scikit-learn", "sklearn", "scikit-learn"),
        ("OpenPyXL", "openpyxl", "openpyxl"),
        ("PyWin32", "pythoncom", "pywin32"),  # Windows COM 支持
        ("PyGAD", "pygad", "pygad"),  # 遗传算法库
    ]
    
    failed_packages = []
    
    for package_name, import_name, pip_name in dependencies:
        if not check_and_install_package(package_name, import_name, pip_name):
            failed_packages.append(package_name)
    
    print()
    print("=== 检查结果 ===")
    
    if not failed_packages:
        print("✓ 所有依赖包都已成功安装！")
        print("现在可以运行整合界面了：")
        print("  python integrated_ui.py")
    else:
        print(f"✗ 以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包：")
        for package in failed_packages:
            # 找到对应的pip名称
            for pkg_name, _, pip_name in dependencies:
                if pkg_name == package:
                    print(f"  pip install {pip_name}")
                    break
    
    print()
    
    # 特别处理 PyWin32 的后安装步骤
    try:
        import pythoncom
        print("✓ PyWin32 (pythoncom) 可用")
    except ImportError:
        print("⚠ PyWin32 安装后需要额外配置")
        print("请尝试运行以下命令：")
        print("  python -m pip install --upgrade pywin32")
        print("  python Scripts/pywin32_postinstall.py -install")
    
    # 检查 PyQt5 的弃用警告问题
    print()
    print("=== PyQt5 弃用警告说明 ===")
    print("如果看到 'sipPyTypeDict() is deprecated' 警告，这是正常的。")
    print("这个警告不影响程序运行，可以忽略。")
    print("如果想要消除警告，可以考虑升级到 PyQt6，但需要修改代码。")

if __name__ == "__main__":
    main()
