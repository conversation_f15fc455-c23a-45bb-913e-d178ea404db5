# Adams 仿真与优化工具集成平台

## 概述

本项目将两个独立的工具整合到一个统一的界面中：
1. **批量仿真工具** - 用于Adams仿真的批量参数设置、仿真执行和相对误差计算
2. **参数优化工具** - 用于机器学习+遗传算法的参数优化

## 文件结构

```
function_param/
├── integrated_ui.py              # 整合主界面
├── shared_styles.py              # 统一样式定义文件
├── test_integrated_ui.py         # 测试脚本
├── test_style_consistency.py     # 样式一致性测试脚本
├── README_整合界面.md            # 本说明文件
├── 启动整合界面.bat              # Windows启动脚本
├── batch_simulation_tool/        # 批量仿真工具模块
│   ├── batch_simulation_ui.py    # 批量仿真界面
│   ├── batch_simulation_logic.py # 批量仿真逻辑
│   ├── adams_utils.py           # Adams工具函数
│   ├── main.py                  # 批量仿真独立启动入口
│   └── ...
└── optimization/                 # 优化工具模块
    ├── optimization_ui.py        # 优化界面
    ├── ml_ga_optimization.py     # 机器学习+遗传算法优化逻辑
    ├── requirements.txt          # 依赖包列表
    └── ...
```

## 使用方法

### 启动整合界面

```bash
python integrated_ui.py
```

### 主要特性

1. **统一界面**：两个原本独立的程序现在整合在一个窗口中
2. **选项卡设计**：用户可以方便地在批量仿真和参数优化功能之间切换
3. **保持原有功能**：两个程序的所有原始功能都得到保留
4. **统一样式主题**：
   - 使用统一的淡蓝色主题（#87CEEB 天空蓝系）
   - 统一的字体（Microsoft YaHei UI 11pt）
   - 一致的按钮、表格、输入框样式
   - 表格序号无底色，易于阅读
   - 共享样式文件确保完全一致的视觉效果
5. **错误处理**：包含完善的错误处理和用户提示
6. **安全退出**：在有任务运行时会提示用户确认退出
7. **样式一致性**：通过共享样式文件确保两个界面完全统一

### 功能说明

#### 1. 批量仿真选项卡

**主要功能：**
- 参数范围设置：设置8个仿真参数的采样范围
- 工况配置：设置转速、负载扭矩和试验RMS值
- 参数文件生成：使用拉丁超立方采样生成参数组合
- 批量仿真执行：自动执行Adams仿真
- 相对误差计算：计算仿真结果与试验值的相对误差

**使用步骤：**
1. 设置结果保存路径
2. 配置参数采样范围（8个参数）
3. 输入工况数据（转速、负载扭矩、试验RMS值）
4. 点击"生成参数文件"创建batch_params.xlsx
5. 点击"启动批量仿真"开始仿真
6. 仿真完成后点击"计算相对误差"

#### 2. 参数优化选项卡

**主要功能：**
- 机器学习模型训练：使用随机森林、极端随机森林或堆叠集成
- 遗传算法优化：基于训练好的模型进行参数优化
- 结果展示：显示最优参数组合和性能指标

**使用步骤：**
1. 选择仿真结果文件（error_summary.xlsx）
2. 配置优化算法参数：
   - 拟合模型：选择机器学习模型
   - 测试集比例：设置训练/测试数据分割比例
   - 迭代次数：设置遗传算法迭代次数
   - 种群规模：设置遗传算法种群大小
3. 点击"开始优化"执行优化过程
4. 查看优化结果表格

## 依赖要求

### Python版本
- Python 3.7+

### 必需的包
```
PyQt5==5.15.9
numpy>=1.23,<2.0
pandas>=1.5,<2.1
scikit-learn>=1.2,<1.4
scipy>=1.10,<1.12
pygad>=3.0.0
openpyxl>=3.1.0
```

### 安装依赖
```bash
pip install -r optimization/requirements.txt
```

## 注意事项

1. **文件路径**：确保Adams模型文件和相关脚本在正确的路径下
2. **权限**：确保程序对保存目录有写入权限
3. **内存**：大规模仿真可能需要较多内存
4. **时间**：批量仿真和优化过程可能需要较长时间

## 故障排除

### 常见问题

1. **模块导入失败**
   - 检查Python路径设置
   - 确认所有依赖包已正确安装

2. **界面显示异常**
   - 检查PyQt5是否正确安装
   - 尝试重新启动程序

3. **仿真执行失败**
   - 检查Adams软件是否正确安装
   - 确认模型文件路径正确

4. **优化过程出错**
   - 检查输入数据格式
   - 确认参数范围设置合理

## 技术支持

如遇到问题，请检查：
1. 控制台输出的错误信息
2. 相关日志文件
3. 输入数据的格式和范围

## 版本信息

- 版本：1.0.0
- 创建日期：2025年
- 最后更新：2025年
