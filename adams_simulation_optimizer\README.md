# Adams 仿真与优化工具集成平台 v2.0

一个专业的Adams多体动力学仿真与参数优化集成工具，提供批量仿真和智能参数优化功能。

## 🚀 主要特性

### 批量仿真
- **多工况支持**：支持设置多个转速和负载扭矩工况
- **参数化仿真**：使用拉丁超立方采样生成参数组合
- **实时进度显示**：显示仿真进度和状态
- **结果自动保存**：仿真结果自动保存为Excel格式
- **RMS自动计算**：自动计算振动RMS值

### 参数优化
- **机器学习模型**：支持随机森林、极端随机森林、堆叠集成
- **遗传算法优化**：基于训练好的模型进行参数优化
- **智能参数搜索**：自动寻找最优参数组合
- **结果可视化**：清晰展示优化结果和性能指标

### 界面特性
- **统一界面**：现代化的深蓝色主题界面
- **选项卡设计**：便于在不同功能间切换
- **实时反馈**：进度显示和状态更新
- **错误处理**：完善的错误提示和恢复机制

## 📁 项目结构

```
adams_simulation_optimizer/
├── main.py                          # 主入口文件
├── config.py                        # 配置管理
├── requirements.txt                 # 依赖列表
├── README.md                        # 项目说明
├── utils/                           # 工具模块
│   ├── __init__.py
│   ├── adams_interface.py           # Adams接口
│   ├── data_processor.py            # 数据处理
│   └── file_manager.py              # 文件管理
├── simulation/                      # 仿真模块
│   ├── __init__.py
│   ├── batch_simulator.py           # 批量仿真核心
│   └── simulation_ui.py             # 仿真界面
├── optimization/                    # 优化模块
│   ├── __init__.py
│   ├── ml_optimizer.py              # 机器学习优化器
│   └── optimization_ui.py           # 优化界面
├── gui/                            # 界面模块
│   ├── __init__.py
│   ├── main_window.py              # 主窗口
│   └── common_widgets.py           # 通用组件
├── docs/                           # 文档目录
├── scripts/                        # 脚本目录
│   ├── install_dependencies.py     # 依赖安装
│   └── start_app.bat               # 启动脚本
└── tests/                          # 测试目录
```

## 🛠️ 安装和使用

### 系统要求
- **操作系统**：Windows 10/11 (64位)
- **Python版本**：Python 3.7 或更高版本
- **内存**：建议 8GB 以上
- **硬盘空间**：至少 2GB 可用空间
- **Adams软件**：需要安装 MSC Adams（用于批量仿真功能）

### 快速开始

#### 方法1：使用启动脚本（推荐）
1. 双击运行 `scripts/start_app.bat`
2. 脚本会自动检查和安装依赖
3. 等待程序启动

#### 方法2：手动安装
1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **配置PyWin32**（Windows）：
   ```bash
   python scripts/install_dependencies.py
   ```

3. **启动程序**：
   ```bash
   python main.py
   ```

### 使用指南

#### 批量仿真
1. 切换到"批量仿真"选项卡
2. 设置结果保存路径
3. 配置参数采样范围（8个接触参数）
4. 输入工况数据（转速、负载扭矩、试验RMS值）
5. 点击"生成参数文件"创建参数组合
6. 点击"启动批量仿真"开始仿真
7. 仿真完成后点击"计算相对误差"

#### 参数优化
1. 切换到"参数优化"选项卡
2. 选择仿真结果文件（error_summary.xlsx）
3. 配置优化算法参数：
   - 拟合模型：选择机器学习模型
   - 测试集比例：设置训练/测试数据分割比例
   - 迭代次数：设置遗传算法迭代次数
   - 种群规模：设置遗传算法种群大小
4. 点击"开始优化"执行优化过程
5. 查看优化结果表格

## 📊 输出文件

### 批量仿真输出
- **batch_params.xlsx**：参数组合文件
- **error_summary.xlsx**：误差分析文件
- **sim_xxx/**：各次仿真的详细结果

### 参数优化输出
- **优化结果表格**：显示各参数的最优值
- **性能指标**：RMSE、R²等模型性能指标

## ⚠️ 注意事项

1. **Adams连接**：确保Adams软件已启动并开启端口5002
2. **文件权限**：确保程序对保存目录有写入权限
3. **内存使用**：大规模仿真可能需要较多内存
4. **时间消耗**：批量仿真和优化过程可能需要较长时间

## 🔧 故障排除

### 常见问题

1. **模块导入失败**
   - 运行：`python scripts/install_dependencies.py`
   - 检查Python路径设置

2. **PyWin32错误**
   - 安装：`pip install pywin32`
   - 配置：`python Scripts/pywin32_postinstall.py -install`

3. **Adams连接失败**
   - 确保Adams软件已启动
   - 检查端口5002是否开启
   - 确认防火墙设置

4. **界面显示异常**
   - 检查PyQt5是否正确安装
   - 尝试重新启动程序
   - 检查系统显示设置

## 📚 文档

详细文档请查看 `docs/` 目录：
- 用户手册
- 快速入门指南
- API参考文档

## 🆕 版本历史

### v2.0.0 (当前版本)
- 重构代码架构，提高可维护性
- 统一配置管理
- 改进错误处理
- 优化界面设计
- 完善文档

### v1.0.0
- 初始版本发布
- 基本的批量仿真功能
- 参数优化功能

## 📞 技术支持

如遇到问题，请：
1. 查看文档和故障排除指南
2. 运行依赖检查脚本
3. 检查程序运行日志
4. 联系技术支持团队

---

**Adams Simulation Team**  
**版本：2.0.0**  
**更新日期：2025年**
