#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块

提供应用程序的主界面，整合批量仿真和参数优化功能。
重构自原来的 integrated_ui.py 文件。

作者: Adams Simulation Team
版本: 2.0.0
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QTabWidget,
                             QMessageBox, QLabel, QStatusBar)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

# 添加项目根目录到Python路径（用于独立运行）
if __name__ == "__main__":
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.insert(0, project_root)

# 导入配置
from config import AppConfig, StyleConfig

# 导入子模块 - 使用延迟导入避免循环导入
SIMULATION_AVAILABLE = False
OPTIMIZATION_AVAILABLE = False

def _import_simulation_widget():
    """延迟导入仿真组件"""
    try:
        from simulation.simulation_ui import SimulationWidget
        return SimulationWidget
    except ImportError as e:
        print(f"❌ 仿真模块导入失败: {e}")
        return None

def _import_optimization_widget():
    """延迟导入优化组件"""
    try:
        from optimization.optimization_ui import OptimizationWidget
        return OptimizationWidget
    except ImportError as e:
        print(f"❌ 优化模块导入失败: {e}")
        return None


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    tab_changed = pyqtSignal(int, str)  # 选项卡切换信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowTitle(AppConfig.APP_NAME)
        self.setGeometry(100, 100, AppConfig.WINDOW_WIDTH, AppConfig.WINDOW_HEIGHT)
        
        # 设置应用程序图标（如果有的话）
        self._set_window_icon()
        
        # 初始化界面
        self._init_ui()
        
        # 设置状态栏
        self._init_status_bar()
        
        print(f"✅ 主窗口初始化完成")
    
    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "..", "resources", "icon.png")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            print(f"⚠️  设置窗口图标失败: {e}")
    
    def _init_ui(self):
        """初始化用户界面"""
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(False)  # 禁止拖动选项卡
        main_layout.addWidget(self.tab_widget)
        
        # 添加功能选项卡
        self._add_simulation_tab()
        self._add_optimization_tab()
        
        # 设置默认选中第一个选项卡
        self.tab_widget.setCurrentIndex(0)
        
        # 连接选项卡切换信号
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
    
    def _add_simulation_tab(self):
        """添加批量仿真选项卡"""
        try:
            print("🔄 正在创建仿真界面...")

            # 延迟导入仿真组件
            SimulationWidget = _import_simulation_widget()
            if SimulationWidget is None:
                self._add_error_tab("批量仿真", "仿真模块不可用，请检查依赖安装")
                return

            # 创建仿真界面组件
            self.simulation_widget = SimulationWidget()

            # 添加到选项卡
            self.tab_widget.addTab(self.simulation_widget, "批量仿真")

            print("✅ 仿真界面创建成功")

        except Exception as e:
            print(f"❌ 创建仿真界面失败: {e}")
            import traceback
            traceback.print_exc()
            self._add_error_tab("批量仿真", f"模块加载失败: {e}")
    
    def _add_optimization_tab(self):
        """添加参数优化选项卡"""
        try:
            print("🔄 正在创建优化界面...")

            # 延迟导入优化组件
            OptimizationWidget = _import_optimization_widget()
            if OptimizationWidget is None:
                self._add_error_tab("参数优化", "优化模块不可用，请检查依赖安装")
                return

            # 创建优化界面组件
            self.optimization_widget = OptimizationWidget()

            # 添加到选项卡
            self.tab_widget.addTab(self.optimization_widget, "参数优化")

            print("✅ 优化界面创建成功")

        except Exception as e:
            print(f"❌ 创建优化界面失败: {e}")
            import traceback
            traceback.print_exc()
            self._add_error_tab("参数优化", f"模块加载失败: {e}")
    
    def _add_error_tab(self, tab_name: str, error_message: str):
        """添加错误提示选项卡"""
        error_widget = QWidget()
        error_layout = QVBoxLayout(error_widget)
        error_layout.setAlignment(Qt.AlignCenter)
        
        # 错误图标和文本
        error_label = QLabel(f"❌ 错误: {error_message}")
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setStyleSheet(f"""
            color: {StyleConfig.ERROR_COLOR};
            font-size: 16px;
            font-weight: bold;
            padding: 50px;
            background-color: {StyleConfig.SURFACE_COLOR};
            border: 2px dashed {StyleConfig.ERROR_COLOR};
            border-radius: 8px;
            margin: 20px;
        """)
        
        # 解决建议
        suggestion_label = QLabel(
            "💡 解决建议:\n"
            "1. 运行依赖检查: python scripts/install_dependencies.py\n"
            "2. 检查Python环境和模块安装\n"
            "3. 查看控制台错误信息\n"
            "4. 重新启动应用程序"
        )
        suggestion_label.setAlignment(Qt.AlignCenter)
        suggestion_label.setStyleSheet(f"""
            color: {StyleConfig.TEXT_SECONDARY};
            font-size: 12px;
            padding: 20px;
            background-color: {StyleConfig.ALTERNATE_BG};
            border-radius: 4px;
            margin: 10px 20px;
        """)
        
        error_layout.addWidget(error_label)
        error_layout.addWidget(suggestion_label)
        
        self.tab_widget.addTab(error_widget, tab_name)
    
    def _init_status_bar(self):
        """初始化状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 显示初始状态
        self.status_bar.showMessage("就绪")
        
        # 添加永久状态信息
        version_label = QLabel(f"v{AppConfig.APP_VERSION}")
        version_label.setStyleSheet(f"color: {StyleConfig.TEXT_SECONDARY}; padding: 2px 8px;")
        self.status_bar.addPermanentWidget(version_label)
    
    def _on_tab_changed(self, index: int):
        """选项卡切换事件处理"""
        if index >= 0 and index < self.tab_widget.count():
            tab_text = self.tab_widget.tabText(index)
            self.status_bar.showMessage(f"当前选项卡: {tab_text}")
            
            # 发射信号
            self.tab_changed.emit(index, tab_text)
            
            print(f"🔄 切换到选项卡: {tab_text}")
    
    def get_current_tab_name(self) -> str:
        """获取当前选项卡名称"""
        current_index = self.tab_widget.currentIndex()
        if current_index >= 0:
            return self.tab_widget.tabText(current_index)
        return ""
    
    def switch_to_tab(self, tab_name: str) -> bool:
        """切换到指定选项卡"""
        for i in range(self.tab_widget.count()):
            if self.tab_widget.tabText(i) == tab_name:
                self.tab_widget.setCurrentIndex(i)
                return True
        return False
    
    def get_simulation_widget(self):
        """获取仿真界面组件"""
        return getattr(self, 'simulation_widget', None)
    
    def get_optimization_widget(self):
        """获取优化界面组件"""
        return getattr(self, 'optimization_widget', None)
    
    def closeEvent(self, event):
        """应用程序关闭事件处理"""
        try:
            # 检查是否有正在运行的任务
            should_close = self._check_running_tasks()
            
            if should_close:
                # 清理资源
                self._cleanup_resources()
                event.accept()
                print("✅ 应用程序正常退出")
            else:
                event.ignore()
                
        except Exception as e:
            print(f"❌ 关闭时发生错误: {e}")
            event.accept()  # 即使出错也要关闭
    
    def _check_running_tasks(self) -> bool:
        """检查是否有正在运行的任务"""
        running_tasks = []
        
        # 检查仿真任务
        if hasattr(self, 'simulation_widget'):
            if hasattr(self.simulation_widget, 'is_simulation_running') and \
               self.simulation_widget.is_simulation_running():
                running_tasks.append("批量仿真")
        
        # 检查优化任务
        if hasattr(self, 'optimization_widget'):
            if hasattr(self.optimization_widget, 'is_optimization_running') and \
               self.optimization_widget.is_optimization_running():
                running_tasks.append("参数优化")
        
        # 如果有正在运行的任务，询问用户
        if running_tasks:
            task_list = "、".join(running_tasks)
            reply = QMessageBox.question(
                self,
                '确认退出',
                f'以下任务正在运行中：{task_list}\n\n确定要退出吗？',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                return False
            
            # 用户确认退出，停止所有任务
            self._stop_all_tasks()
        
        return True
    
    def _stop_all_tasks(self):
        """停止所有正在运行的任务"""
        try:
            # 停止仿真任务
            if hasattr(self, 'simulation_widget'):
                if hasattr(self.simulation_widget, 'stop_simulation'):
                    self.simulation_widget.stop_simulation()
            
            # 停止优化任务
            if hasattr(self, 'optimization_widget'):
                if hasattr(self.optimization_widget, 'stop_optimization'):
                    self.optimization_widget.stop_optimization()
                    
            print("🛑 已停止所有正在运行的任务")
            
        except Exception as e:
            print(f"⚠️  停止任务时发生错误: {e}")
    
    def _cleanup_resources(self):
        """清理资源"""
        try:
            # 清理仿真资源
            if hasattr(self, 'simulation_widget'):
                if hasattr(self.simulation_widget, 'cleanup'):
                    self.simulation_widget.cleanup()
            
            # 清理优化资源
            if hasattr(self, 'optimization_widget'):
                if hasattr(self.optimization_widget, 'cleanup'):
                    self.optimization_widget.cleanup()
                    
            print("🧹 资源清理完成")
            
        except Exception as e:
            print(f"⚠️  清理资源时发生错误: {e}")


# 导出的公共接口
__all__ = ['MainWindow']


# 独立运行测试代码
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication

    print("🔄 独立运行主窗口测试...")

    try:
        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        app = QApplication(sys.argv)

        # 设置应用程序信息
        app.setApplicationName(AppConfig.APP_NAME)
        app.setApplicationVersion(AppConfig.APP_VERSION)

        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        print("✅ 主窗口创建成功，正在显示...")
        print("💡 提示：这是独立运行模式，用于调试界面")
        print("💡 正常使用请运行: python main.py")

        # 运行应用程序
        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ 主窗口测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
