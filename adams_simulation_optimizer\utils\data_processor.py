#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理工具模块

提供参数组合生成、误差计算等数据处理功能。
整合了原来分散在各个文件中的数据处理逻辑。

作者: Adams Simulation Team
版本: 2.0.0
"""

import numpy as np
import pandas as pd
from scipy.stats import qmc
from itertools import product
from typing import List, Dict, Tuple, Any


def generate_parameter_combinations(param_ranges: Dict[str, Dict], 
                                   conditions: List[Dict], 
                                   n_samples: int) -> pd.DataFrame:
    """
    生成参数组合，使用拉丁超立方采样。
    
    Args:
        param_ranges (dict): 参数范围字典，格式为 {param_name: {"min": min_val, "max": max_val, "enabled": bool}}
        conditions (list): 工况列表，每个元素为 {"speed": rpm, "torque": nm, "test_rms": rms}
        n_samples (int): 采样数量
        
    Returns:
        pd.DataFrame: 包含所有参数组合和工况的DataFrame
    """
    # 筛选出启用的参数
    enabled_params = {name: ranges for name, ranges in param_ranges.items() 
                     if ranges.get("enabled", True)}
    
    if not enabled_params:
        raise ValueError("至少需要启用一个参数")
    
    # 使用拉丁超立方采样生成参数组合
    param_names = list(enabled_params.keys())
    n_params = len(param_names)
    
    # 创建拉丁超立方采样器
    sampler = qmc.LatinHypercube(d=n_params, seed=42)
    samples = sampler.random(n=n_samples)
    
    # 将采样结果映射到实际参数范围
    param_combinations = []
    for sample in samples:
        combination = {}
        for i, param_name in enumerate(param_names):
            min_val = enabled_params[param_name]["min"]
            max_val = enabled_params[param_name]["max"]
            # 线性映射到参数范围
            param_value = min_val + sample[i] * (max_val - min_val)
            combination[param_name] = param_value
        
        # 添加未启用的参数（使用默认值）
        for param_name, ranges in param_ranges.items():
            if param_name not in combination:
                combination[param_name] = ranges.get("default", ranges.get("min", 0))
        
        param_combinations.append(combination)
    
    # 生成笛卡尔积（参数组合 × 工况）
    all_combinations = []
    for i, params in enumerate(param_combinations):
        for j, condition in enumerate(conditions):
            combo = params.copy()
            combo.update({
                "参数组合ID": i + 1,
                "工况ID": j + 1,
                "工况_转速(rpm)": condition["speed"],
                "工况_负载扭矩(N·m)": condition["torque"],
                "试验RMS值": condition["test_rms"],
                "仿真RMS值": np.nan,  # 待填入
                "相对误差(%)": np.nan  # 待计算
            })
            all_combinations.append(combo)
    
    return pd.DataFrame(all_combinations)


def calculate_relative_errors(df: pd.DataFrame, 
                            sim_rms_col: str = "仿真RMS值",
                            test_rms_col: str = "试验RMS值",
                            error_col: str = "相对误差(%)") -> pd.DataFrame:
    """
    计算相对误差。
    
    Args:
        df (pd.DataFrame): 包含仿真和试验RMS值的DataFrame
        sim_rms_col (str): 仿真RMS值列名
        test_rms_col (str): 试验RMS值列名
        error_col (str): 相对误差列名
        
    Returns:
        pd.DataFrame: 添加了相对误差的DataFrame
    """
    df = df.copy()
    
    # 计算相对误差：|(仿真值 - 试验值) / 试验值| * 100%
    sim_values = pd.to_numeric(df[sim_rms_col], errors='coerce')
    test_values = pd.to_numeric(df[test_rms_col], errors='coerce')
    
    # 避免除零错误
    mask = (test_values != 0) & pd.notna(sim_values) & pd.notna(test_values)
    df.loc[mask, error_col] = np.abs((sim_values[mask] - test_values[mask]) / test_values[mask]) * 100
    
    return df


def calculate_total_relative_error(df: pd.DataFrame, 
                                 error_col: str = "相对误差(%)",
                                 group_col: str = "参数组合ID") -> pd.DataFrame:
    """
    计算每个参数组合的总相对误差（多工况的平均值）。
    
    Args:
        df (pd.DataFrame): 包含相对误差的DataFrame
        error_col (str): 相对误差列名
        group_col (str): 分组列名（通常是参数组合ID）
        
    Returns:
        pd.DataFrame: 包含总相对误差的汇总DataFrame
    """
    # 按参数组合分组，计算平均相对误差
    grouped = df.groupby(group_col).agg({
        error_col: 'mean',
        **{col: 'first' for col in df.columns 
           if col.startswith(('sun_', 'ring_')) or col in ['参数组合ID']}
    }).reset_index(drop=True)
    
    # 重命名误差列
    grouped = grouped.rename(columns={error_col: "总相对误差"})
    
    return grouped


def validate_parameter_ranges(param_ranges: Dict[str, Dict]) -> List[str]:
    """
    验证参数范围的有效性。
    
    Args:
        param_ranges (dict): 参数范围字典
        
    Returns:
        list: 错误信息列表，空列表表示验证通过
    """
    errors = []
    
    for param_name, ranges in param_ranges.items():
        if "min" not in ranges or "max" not in ranges:
            errors.append(f"参数 {param_name} 缺少 min 或 max 值")
            continue
            
        min_val = ranges["min"]
        max_val = ranges["max"]
        
        # 检查数值类型
        try:
            min_val = float(min_val)
            max_val = float(max_val)
        except (ValueError, TypeError):
            errors.append(f"参数 {param_name} 的范围值必须是数字")
            continue
        
        # 检查范围有效性
        if min_val > max_val:
            errors.append(f"参数 {param_name} 的最小值不能大于最大值")
        
        if min_val < 0 and param_name in ["sun_damping", "ring_damping"]:
            errors.append(f"参数 {param_name} 的值不能为负数")
    
    return errors


def validate_conditions(conditions: List[Dict]) -> List[str]:
    """
    验证工况数据的有效性。
    
    Args:
        conditions (list): 工况列表
        
    Returns:
        list: 错误信息列表，空列表表示验证通过
    """
    errors = []
    
    if not conditions:
        errors.append("至少需要设置一个工况")
        return errors
    
    for i, condition in enumerate(conditions):
        # 检查必需字段
        required_fields = ["speed", "torque", "test_rms"]
        for field in required_fields:
            if field not in condition:
                errors.append(f"工况 {i+1} 缺少字段: {field}")
                continue
        
        # 检查数值有效性
        try:
            speed = float(condition["speed"])
            torque = float(condition["torque"])
            test_rms = float(condition["test_rms"])
            
            if speed < 0:
                errors.append(f"工况 {i+1} 的转速不能为负数")
            if torque < 0:
                errors.append(f"工况 {i+1} 的扭矩不能为负数")
            if test_rms < 0:
                errors.append(f"工况 {i+1} 的试验RMS值不能为负数")
                
        except (ValueError, TypeError):
            errors.append(f"工况 {i+1} 包含无效的数值")
    
    return errors


def extract_parameter_bounds(df: pd.DataFrame, 
                           param_columns: List[str] = None,
                           expand_ratio: float = 0.1) -> List[Tuple[float, float]]:
    """
    从数据中提取参数边界，用于优化算法。
    
    Args:
        df (pd.DataFrame): 包含参数数据的DataFrame
        param_columns (list): 参数列名列表，None时自动检测
        expand_ratio (float): 边界扩展比例，默认10%
        
    Returns:
        list: 参数边界列表，每个元素为 (min_val, max_val) 元组
    """
    if param_columns is None:
        # 自动检测参数列（以sun_或ring_开头的列）
        param_columns = [col for col in df.columns 
                        if col.startswith(('sun_', 'ring_'))]
    
    bounds = []
    for col in param_columns:
        if col in df.columns:
            min_val = df[col].min()
            max_val = df[col].max()
            span = max_val - min_val
            
            # 扩展边界
            expanded_min = min_val - expand_ratio * span
            expanded_max = max_val + expand_ratio * span
            
            bounds.append((expanded_min, expanded_max))
        else:
            # 如果列不存在，使用默认边界
            bounds.append((0.0, 1.0))
    
    return bounds


def prepare_ml_data(df: pd.DataFrame, 
                   target_col: str = "总相对误差",
                   param_columns: List[str] = None) -> Tuple[np.ndarray, np.ndarray, List[str]]:
    """
    为机器学习准备数据。
    
    Args:
        df (pd.DataFrame): 原始数据
        target_col (str): 目标列名
        param_columns (list): 特征列名列表，None时自动检测
        
    Returns:
        tuple: (X, y, feature_names) - 特征矩阵、目标向量、特征名称列表
    """
    if param_columns is None:
        # 自动检测参数列
        param_columns = [col for col in df.columns 
                        if col.startswith(('sun_', 'ring_'))]
    
    # 检查目标列是否存在
    if target_col not in df.columns:
        raise ValueError(f"目标列 '{target_col}' 不存在")
    
    # 提取特征和目标
    X = df[param_columns].values
    y = df[target_col].values
    
    # 移除包含NaN的行
    mask = ~(np.isnan(X).any(axis=1) | np.isnan(y))
    X = X[mask]
    y = y[mask]
    
    return X, y, param_columns


# 导出的公共接口
__all__ = [
    'generate_parameter_combinations',
    'calculate_relative_errors',
    'calculate_total_relative_error',
    'validate_parameter_ranges',
    'validate_conditions',
    'extract_parameter_bounds',
    'prepare_ml_data'
]
