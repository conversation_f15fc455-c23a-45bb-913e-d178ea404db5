#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享样式定义 - 确保所有界面使用统一的颜色主题和样式
"""

from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import QApplication

# 统一的颜色主题 - 与图片一致的深蓝色系
THEME_COLORS = {
    'primary': '#0078D4',           # 主色调 - 微软蓝色 (与图片按钮颜色一致)
    'primary_hover': '#106EBE',     # 主色调悬停 - 稍深的蓝色
    'primary_pressed': '#005A9E',   # 主色调按下 - 更深的蓝色
    'secondary': '#6C757D',         # 次要颜色 - 灰色
    'background': '#F3F2F1',        # 背景色 - 浅灰色 (与图片背景一致)
    'surface': '#FFFFFF',           # 表面色
    'border': '#EDEBE9',            # 边框色 - 浅灰色
    'border_light': '#E1DFDD',      # 浅边框色 - 更浅的灰色
    'text_primary': '#323130',      # 主要文本色 - 深灰色 (与图片文字颜色一致)
    'text_secondary': '#605E5C',    # 次要文本色 - 中灰色
    'text_disabled': '#A19F9D',     # 禁用文本色 - 浅灰色
    'alternate_bg': '#FAF9F8',      # 交替背景色 - 非常浅的灰色
}

# 统一的字体设置
FONT_FAMILY = "Microsoft YaHei UI"
FONT_SIZE = 11

def apply_unified_font():
    """应用统一的字体设置"""
    QApplication.instance().setFont(QFont(FONT_FAMILY, FONT_SIZE))

# 统一的样式表
UNIFIED_STYLESHEET = f"""
/* 主窗口背景 */
QMainWindow {{
    background-color: {THEME_COLORS['background']};
}}

/* 选项卡样式 */
QTabWidget::pane {{
    border: 1px solid {THEME_COLORS['border']};
    border-radius: 4px;
    background-color: {THEME_COLORS['surface']};
    margin-top: 2px;
}}

QTabWidget::tab-bar {{
    alignment: left;
}}

QTabBar::tab {{
    background-color: #E9ECEF;
    color: {THEME_COLORS['text_secondary']};
    border: 1px solid {THEME_COLORS['border']};
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 16px;
    margin-right: 2px;
    font-weight: bold;
    min-width: 80px;
}}

QTabBar::tab:selected {{
    background-color: {THEME_COLORS['surface']};
    color: {THEME_COLORS['primary']};
    border-bottom: 2px solid {THEME_COLORS['primary']};
}}

QTabBar::tab:hover:!selected {{
    background-color: {THEME_COLORS['alternate_bg']};
    color: {THEME_COLORS['primary']};
}}

/* 按钮样式 */
QPushButton {{
    background-color: {THEME_COLORS['primary']};
    color: {THEME_COLORS['surface']};
    border: 1px solid {THEME_COLORS['primary']};
    border-radius: 4px;
    padding: 6px 12px;
    font-weight: bold;
    min-height: 20px;
}}

QPushButton:hover {{
    background-color: {THEME_COLORS['primary_hover']};
    border-color: {THEME_COLORS['primary_hover']};
}}

QPushButton:pressed {{
    background-color: {THEME_COLORS['primary_pressed']};
    border-color: {THEME_COLORS['primary_pressed']};
}}

QPushButton:disabled {{
    background-color: {THEME_COLORS['secondary']};
    border-color: {THEME_COLORS['secondary']};
    color: {THEME_COLORS['text_disabled']};
}}

/* 分组框样式 */
QGroupBox {{
    border: 1px solid {THEME_COLORS['border']};
    border-radius: 4px;
    margin-top: 15px;
    padding-top: 20px;
    font-weight: bold;
}}

QGroupBox:title {{
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px;
    color: {THEME_COLORS['primary']};
    font-weight: bold;
}}

/* 输入控件样式 */
QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
    border: 1px solid {THEME_COLORS['border_light']};
    border-radius: 4px;
    padding: 4px;
    background-color: {THEME_COLORS['surface']};
    min-height: 16px;
}}

QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
    border-color: {THEME_COLORS['primary']};
    outline: none;
}}

/* 表格样式 */
QTableWidget {{
    border: 1px solid {THEME_COLORS['border_light']};
    background-color: {THEME_COLORS['surface']};
    alternate-background-color: {THEME_COLORS['alternate_bg']};
    gridline-color: {THEME_COLORS['border']};
}}

/* 水平表头（列标题）- 使用深蓝色背景，白色文字 */
QHeaderView::section:horizontal {{
    background-color: {THEME_COLORS['primary']};
    color: {THEME_COLORS['surface']};
    border: 1px solid {THEME_COLORS['border_light']};
    padding: 6px;
    font-weight: bold;
}}

/* 垂直表头（行号）- 去掉蓝色底色，使用白色背景 */
QHeaderView::section:vertical {{
    background-color: {THEME_COLORS['surface']};
    color: {THEME_COLORS['text_primary']};
    border: 1px solid {THEME_COLORS['border_light']};
    padding: 6px;
    font-weight: normal;
}}

/* 标签样式 */
QLabel {{
    color: {THEME_COLORS['text_primary']};
}}

/* 复选框样式 */
QCheckBox {{
    color: {THEME_COLORS['text_primary']};
}}

QCheckBox::indicator:checked {{
    background-color: {THEME_COLORS['primary']};
    border: 1px solid {THEME_COLORS['primary']};
}}

/* 滚动条样式 */
QScrollBar:vertical {{
    background-color: {THEME_COLORS['alternate_bg']};
    width: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:vertical {{
    background-color: {THEME_COLORS['border_light']};
    border-radius: 6px;
    min-height: 20px;
}}

QScrollBar::handle:vertical:hover {{
    background-color: {THEME_COLORS['primary']};
}}

/* 进度条样式 */
QProgressBar {{
    border: 1px solid {THEME_COLORS['border_light']};
    border-radius: 4px;
    text-align: center;
    background-color: {THEME_COLORS['alternate_bg']};
}}

QProgressBar::chunk {{
    background-color: {THEME_COLORS['primary']};
    border-radius: 3px;
}}

/* 状态栏样式 */
QStatusBar {{
    background-color: {THEME_COLORS['surface']};
    border-top: 1px solid {THEME_COLORS['border']};
    color: {THEME_COLORS['text_primary']};
}}

/* 菜单样式 */
QMenuBar {{
    background-color: {THEME_COLORS['surface']};
    border-bottom: 1px solid {THEME_COLORS['border']};
}}

QMenuBar::item {{
    background-color: transparent;
    padding: 4px 8px;
}}

QMenuBar::item:selected {{
    background-color: {THEME_COLORS['primary']};
    color: {THEME_COLORS['surface']};
}}

/* 工具提示样式 */
QToolTip {{
    background-color: {THEME_COLORS['text_primary']};
    color: {THEME_COLORS['surface']};
    border: 1px solid {THEME_COLORS['border']};
    border-radius: 4px;
    padding: 4px;
}}
"""

def apply_unified_style(app_or_widget):
    """
    应用统一的样式表
    
    Args:
        app_or_widget: QApplication实例或QWidget实例
    """
    apply_unified_font()
    app_or_widget.setStyleSheet(UNIFIED_STYLESHEET)

# 为了向后兼容，保留原有的样式表变量名
_APP_QSS = UNIFIED_STYLESHEET
_UNIFIED_STYLE_QSS = UNIFIED_STYLESHEET
