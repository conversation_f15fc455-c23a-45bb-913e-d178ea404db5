#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理工具模块

提供Excel文件读写、路径验证等文件操作功能。
统一管理所有文件相关的操作。

作者: Adams Simulation Team
版本: 2.0.0
"""

import os
import pandas as pd
from typing import Optional, Dict, Any, List
import shutil
from pathlib import Path


def validate_file_path(file_path: str, 
                      must_exist: bool = True,
                      extension: Optional[str] = None) -> bool:
    """
    验证文件路径的有效性。
    
    Args:
        file_path (str): 文件路径
        must_exist (bool): 是否必须存在，默认True
        extension (str): 期望的文件扩展名，如'.xlsx'
        
    Returns:
        bool: 路径是否有效
    """
    if not file_path or not isinstance(file_path, str):
        return False
    
    path = Path(file_path)
    
    # 检查文件是否存在
    if must_exist and not path.exists():
        return False
    
    # 检查扩展名
    if extension and not file_path.lower().endswith(extension.lower()):
        return False
    
    # 检查父目录是否存在（对于新文件）
    if not must_exist and not path.parent.exists():
        return False
    
    return True


def validate_directory_path(dir_path: str, 
                          create_if_not_exist: bool = False) -> bool:
    """
    验证目录路径的有效性。
    
    Args:
        dir_path (str): 目录路径
        create_if_not_exist (bool): 如果不存在是否创建
        
    Returns:
        bool: 路径是否有效
    """
    if not dir_path or not isinstance(dir_path, str):
        return False
    
    path = Path(dir_path)
    
    if path.exists():
        return path.is_dir()
    
    if create_if_not_exist:
        try:
            path.mkdir(parents=True, exist_ok=True)
            return True
        except Exception:
            return False
    
    return False


def save_to_excel(data: pd.DataFrame, 
                 file_path: str,
                 sheet_name: str = 'Sheet1',
                 index: bool = False,
                 backup_existing: bool = True) -> bool:
    """
    保存DataFrame到Excel文件。
    
    Args:
        data (pd.DataFrame): 要保存的数据
        file_path (str): 文件路径
        sheet_name (str): 工作表名称
        index (bool): 是否保存索引
        backup_existing (bool): 是否备份现有文件
        
    Returns:
        bool: 保存是否成功
    """
    try:
        # 确保目录存在
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
        
        # 备份现有文件
        if backup_existing and os.path.exists(file_path):
            backup_path = file_path + '.backup'
            shutil.copy2(file_path, backup_path)
        
        # 保存文件
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            data.to_excel(writer, sheet_name=sheet_name, index=index)
        
        print(f"数据已保存到: {file_path}")
        return True
        
    except Exception as e:
        print(f"保存Excel文件失败: {e}")
        return False


def load_from_excel(file_path: str,
                   sheet_name: Optional[str] = None,
                   header: int = 0,
                   required_columns: Optional[List[str]] = None) -> Optional[pd.DataFrame]:
    """
    从Excel文件加载数据。
    
    Args:
        file_path (str): 文件路径
        sheet_name (str): 工作表名称，None表示第一个工作表
        header (int): 表头行号
        required_columns (list): 必需的列名列表
        
    Returns:
        pd.DataFrame or None: 加载的数据，失败时返回None
    """
    try:
        if not validate_file_path(file_path, must_exist=True, extension='.xlsx'):
            print(f"无效的Excel文件路径: {file_path}")
            return None
        
        # 读取Excel文件
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header)
        else:
            df = pd.read_excel(file_path, header=header)
        
        # 检查必需的列
        if required_columns:
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"Excel文件缺少必需的列: {missing_columns}")
                return None
        
        print(f"成功加载Excel文件: {file_path} (形状: {df.shape})")
        return df
        
    except Exception as e:
        print(f"加载Excel文件失败: {e}")
        return None


def get_available_filename(base_path: str, 
                          suffix: str = "",
                          extension: str = ".xlsx") -> str:
    """
    获取可用的文件名（避免覆盖现有文件）。
    
    Args:
        base_path (str): 基础路径（不含扩展名）
        suffix (str): 后缀字符串
        extension (str): 文件扩展名
        
    Returns:
        str: 可用的完整文件路径
    """
    if suffix:
        full_path = f"{base_path}_{suffix}{extension}"
    else:
        full_path = f"{base_path}{extension}"
    
    if not os.path.exists(full_path):
        return full_path
    
    # 如果文件已存在，添加数字后缀
    counter = 1
    while True:
        if suffix:
            test_path = f"{base_path}_{suffix}_{counter}{extension}"
        else:
            test_path = f"{base_path}_{counter}{extension}"
        
        if not os.path.exists(test_path):
            return test_path
        
        counter += 1


def clean_temp_files(directory: str, 
                    patterns: List[str] = None,
                    max_age_days: int = 7) -> int:
    """
    清理临时文件。
    
    Args:
        directory (str): 目录路径
        patterns (list): 文件名模式列表，如['*.tmp', '*.temp']
        max_age_days (int): 最大文件年龄（天）
        
    Returns:
        int: 删除的文件数量
    """
    if not os.path.exists(directory):
        return 0
    
    if patterns is None:
        patterns = ['*.tmp', '*.temp', '*.bak', '*.backup']
    
    import glob
    import time
    
    deleted_count = 0
    current_time = time.time()
    max_age_seconds = max_age_days * 24 * 3600
    
    try:
        for pattern in patterns:
            file_pattern = os.path.join(directory, pattern)
            for file_path in glob.glob(file_pattern):
                try:
                    # 检查文件年龄
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        deleted_count += 1
                        print(f"删除临时文件: {file_path}")
                except Exception as e:
                    print(f"删除文件失败 {file_path}: {e}")
    
    except Exception as e:
        print(f"清理临时文件时出错: {e}")
    
    return deleted_count


def copy_file_with_backup(source: str, 
                         destination: str,
                         backup_existing: bool = True) -> bool:
    """
    复制文件，可选择备份现有文件。
    
    Args:
        source (str): 源文件路径
        destination (str): 目标文件路径
        backup_existing (bool): 是否备份现有文件
        
    Returns:
        bool: 复制是否成功
    """
    try:
        if not os.path.exists(source):
            print(f"源文件不存在: {source}")
            return False
        
        # 确保目标目录存在
        dest_dir = os.path.dirname(destination)
        if dest_dir and not os.path.exists(dest_dir):
            os.makedirs(dest_dir, exist_ok=True)
        
        # 备份现有文件
        if backup_existing and os.path.exists(destination):
            backup_path = destination + '.backup'
            shutil.copy2(destination, backup_path)
            print(f"已备份现有文件: {backup_path}")
        
        # 复制文件
        shutil.copy2(source, destination)
        print(f"文件复制成功: {source} -> {destination}")
        return True
        
    except Exception as e:
        print(f"文件复制失败: {e}")
        return False


def get_file_info(file_path: str) -> Optional[Dict[str, Any]]:
    """
    获取文件信息。
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        dict or None: 文件信息字典，失败时返回None
    """
    try:
        if not os.path.exists(file_path):
            return None
        
        stat = os.stat(file_path)
        
        return {
            'path': file_path,
            'name': os.path.basename(file_path),
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'modified_time': stat.st_mtime,
            'created_time': stat.st_ctime,
            'is_file': os.path.isfile(file_path),
            'is_dir': os.path.isdir(file_path),
            'extension': os.path.splitext(file_path)[1]
        }
        
    except Exception as e:
        print(f"获取文件信息失败: {e}")
        return None


# 导出的公共接口
__all__ = [
    'validate_file_path',
    'validate_directory_path',
    'save_to_excel',
    'load_from_excel',
    'get_available_filename',
    'clean_temp_files',
    'copy_file_with_backup',
    'get_file_info'
]
