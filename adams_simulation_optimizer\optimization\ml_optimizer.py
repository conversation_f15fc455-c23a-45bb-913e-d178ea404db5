#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机器学习优化器模块

使用机器学习回归模型结合遗传算法进行参数优化。
重构自原来的 ml_ga_optimization.py 文件。

作者: Adams Simulation Team
版本: 2.0.0
"""

import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Callable
from sklearn.model_selection import train_test_split, RandomizedSearchCV, cross_val_score
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, StackingRegressor
from sklearn.metrics import mean_squared_error, r2_score
import pygad
from scipy.stats import randint
from PyQt5.QtCore import QObject, pyqtSignal

from config import MLConfig
from utils.file_manager import load_from_excel
from utils.data_processor import prepare_ml_data, extract_parameter_bounds


class MLOptimizer(QObject):
    """机器学习优化器类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int, str)  # 进度更新信号 (百分比, 状态文本)
    optimization_finished = pyqtSignal(dict)  # 优化完成信号
    optimization_error = pyqtSignal(str)      # 优化错误信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_running = False
        self.should_stop = False
        
        # 优化结果
        self.model = None
        self.optimization_result = None
    
    def load_data(self, xlsx_path: str) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        加载数据文件
        
        Args:
            xlsx_path: Excel文件路径
            
        Returns:
            tuple: (X, y, param_names) - 特征矩阵、目标向量、参数名称列表
        """
        if not os.path.exists(xlsx_path):
            raise FileNotFoundError(f"文件不存在: {xlsx_path}")
        
        df = load_from_excel(xlsx_path)
        if df is None:
            raise Exception("无法读取Excel文件")
        
        if '总相对误差' not in df.columns:
            raise ValueError("文件缺少列: 总相对误差")
        
        # 准备机器学习数据
        X, y, param_names = prepare_ml_data(df, target_col='总相对误差')
        
        if len(X) == 0:
            raise ValueError("没有有效的数据用于训练")
        
        return X, y, param_names
    
    def build_model(self, model_name: str, X_train: np.ndarray, y_train: np.ndarray):
        """
        构建并训练模型
        
        Args:
            model_name: 模型名称
            X_train: 训练特征
            y_train: 训练目标
            
        Returns:
            训练好的模型
        """
        model_code = MLConfig.MODEL_NAMES.get(model_name, model_name)
        
        if model_code == "RandomForest":
            model = RandomForestRegressor(**MLConfig.MODEL_PARAMS["RandomForest"])
            
        elif model_code == "ExtraTrees":
            # 使用自动调参的极端随机森林
            model = self._build_auto_tuned_extra_trees(X_train, y_train)
            
        elif model_code == "Stack":
            # 堆叠集成模型
            estimators = [
                ('rf', RandomForestRegressor(**MLConfig.MODEL_PARAMS["RandomForest"])),
                ('etr', ExtraTreesRegressor(**MLConfig.MODEL_PARAMS["ExtraTrees"]))
            ]
            final_estimator = RandomForestRegressor(n_estimators=200, random_state=42)
            model = StackingRegressor(estimators=estimators, final_estimator=final_estimator)
            
        else:
            raise ValueError(f"不支持的模型: {model_name}")
        
        # 训练模型
        model.fit(X_train, y_train)
        return model
    
    def _build_auto_tuned_extra_trees(self, X_train: np.ndarray, y_train: np.ndarray):
        """构建自动调参的极端随机森林模型"""
        # 基础模型
        base_model = ExtraTreesRegressor(**MLConfig.MODEL_PARAMS["ExtraTrees"])
        
        # 超参数搜索空间
        param_dist = {
            'n_estimators': randint(200, 800),
            'max_depth': list(range(10, 51, 10)) + [None],
            'min_samples_split': randint(2, 15),
            'min_samples_leaf': randint(1, 15),
            'max_features': ['sqrt', 'log2', None]
        }
        
        # 随机搜索
        search_model = RandomizedSearchCV(
            estimator=ExtraTreesRegressor(random_state=42),
            param_distributions=param_dist,
            n_iter=50,
            cv=5,
            scoring='r2',
            n_jobs=-1,
            random_state=42,
            verbose=0
        )
        
        # 训练两个模型并比较
        base_model.fit(X_train, y_train)
        search_model.fit(X_train, y_train)
        
        # 交叉验证比较
        base_cv_scores = cross_val_score(base_model, X_train, y_train, cv=5, scoring='r2')
        
        print(f"[Auto-tuning] 固定参数模型的 5 折交叉验证 R²: {base_cv_scores.mean():.4f}")
        print(f"[Auto-tuning] 最佳参数: {search_model.best_params_}")
        print(f"[Auto-tuning] 自动调参模型的 5 折交叉验证 R²: {search_model.best_score_:.4f}")
        
        # 选择更好的模型
        if base_cv_scores.mean() > search_model.best_score_:
            print("[Auto-tuning] 使用固定参数模型")
            return base_model
        else:
            print("[Auto-tuning] 使用自动调参模型")
            return search_model.best_estimator_
    
    def evaluate_model(self, model, X_test: np.ndarray, y_test: np.ndarray) -> Tuple[float, float]:
        """
        评估模型性能
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试目标
            
        Returns:
            tuple: (rmse, r2) - 均方根误差和决定系数
        """
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, y_pred)
        return rmse, r2
    
    def run_genetic_algorithm(self, model, bounds: List[Tuple[float, float]], 
                            generations: int = 200, pop_size: int = 50,
                            crossover_prob: float = 0.8, mutation_prob: float = 0.1) -> Tuple:
        """
        运行遗传算法优化
        
        Args:
            model: 训练好的模型
            bounds: 参数边界列表
            generations: 迭代次数
            pop_size: 种群大小
            crossover_prob: 交叉概率
            mutation_prob: 变异概率
            
        Returns:
            tuple: (best_solution, best_fitness, ga_instance)
        """
        num_params = len(bounds)
        
        # 基因空间定义
        gene_space = [{'low': low, 'high': high} for low, high in bounds]
        
        def fitness_func(ga_instance, solution, solution_idx):
            """适应度函数：最小化预测误差"""
            if self.should_stop:
                return -1e6  # 停止信号
            
            sol = np.array(solution).reshape(1, -1)
            pred = model.predict(sol)[0]
            return -float(pred)  # 负号表示最小化
        
        def on_generation(ga_inst):
            """每代回调函数"""
            if self.should_stop:
                return
            
            if generations > 0:
                progress = int(ga_inst.generations_completed / generations * 100)
                status = f"遗传算法优化中... 第 {ga_inst.generations_completed}/{generations} 代"
                self.progress_updated.emit(progress, status)
        
        # 创建遗传算法实例
        ga = pygad.GA(
            gene_space=gene_space,
            num_generations=generations,
            num_parents_mating=pop_size // 2,
            fitness_func=fitness_func,
            sol_per_pop=pop_size,
            num_genes=num_params,
            crossover_probability=crossover_prob,
            mutation_probability=mutation_prob,
            mutation_type="random",
            mutation_percent_genes=10,
            random_mutation_min_val=[low for low, _ in bounds],
            random_mutation_max_val=[high for _, high in bounds],
            stop_criteria=[f'saturate_{generations//10}'],
            on_generation=on_generation
        )
        
        # 运行优化
        ga.run()
        
        if not self.should_stop:
            self.progress_updated.emit(100, "遗传算法优化完成")
        
        return ga.best_solution()
    
    def optimize(self, xlsx_path: str, test_ratio: float = 0.25, 
                generations: int = 100, pop_size: int = 50, 
                model_name: str = "随机森林") -> Dict:
        """
        执行完整的优化流程
        
        Args:
            xlsx_path: 数据文件路径
            test_ratio: 测试集比例
            generations: 遗传算法迭代次数
            pop_size: 种群大小
            model_name: 模型名称
            
        Returns:
            dict: 优化结果
        """
        try:
            self.is_running = True
            self.should_stop = False
            
            # 1. 加载数据
            self.progress_updated.emit(10, "正在加载数据...")
            X, y, param_names = self.load_data(xlsx_path)
            
            # 2. 数据划分
            self.progress_updated.emit(20, "正在划分训练/测试集...")
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_ratio, random_state=42
            )
            
            # 3. 训练模型
            self.progress_updated.emit(30, f"正在训练{model_name}模型...")
            self.model = self.build_model(model_name, X_train, y_train)
            
            # 4. 评估模型
            self.progress_updated.emit(40, "正在评估模型性能...")
            rmse, r2 = self.evaluate_model(self.model, X_test, y_test)
            
            # 5. 定义搜索边界
            self.progress_updated.emit(50, "正在设置优化边界...")
            bounds = extract_parameter_bounds(
                pd.DataFrame(X, columns=param_names), 
                param_names, 
                expand_ratio=0.1
            )
            
            # 6. 遗传算法优化
            self.progress_updated.emit(60, "开始遗传算法优化...")
            best_solution, _, _ = self.run_genetic_algorithm(
                self.model, bounds, generations, pop_size
            )
            
            if self.should_stop:
                raise Exception("优化被用户中断")
            
            # 7. 计算最优解的预测误差
            best_pred_error = self.model.predict(best_solution.reshape(1, -1))[0]
            
            # 8. 整理结果
            result = {
                "rmse": rmse,
                "r2": r2,
                "param_names": param_names,
                "best_solution": best_solution,
                "best_pred_error": best_pred_error,
                "model_name": model_name,
                "test_ratio": test_ratio,
                "generations": generations,
                "pop_size": pop_size
            }
            
            self.optimization_result = result
            self.progress_updated.emit(100, "优化完成")
            self.optimization_finished.emit(result)
            
            return result
            
        except Exception as e:
            self.optimization_error.emit(str(e))
            raise
        finally:
            self.is_running = False
    
    def stop_optimization(self):
        """停止优化"""
        self.should_stop = True
    
    def get_optimization_result(self) -> Optional[Dict]:
        """获取优化结果"""
        return self.optimization_result
    
    def is_optimization_running(self) -> bool:
        """检查优化是否正在运行"""
        return self.is_running


def optimize_parameters(xlsx_path: str, test_ratio: float = 0.25,
                       generations: int = 100, pop_size: int = 50,
                       model_name: str = "随机森林",
                       progress_callback: Optional[Callable] = None) -> Dict:
    """
    参数优化的便捷函数
    
    Args:
        xlsx_path: 数据文件路径
        test_ratio: 测试集比例
        generations: 遗传算法迭代次数
        pop_size: 种群大小
        model_name: 模型名称
        progress_callback: 进度回调函数
        
    Returns:
        dict: 优化结果
    """
    optimizer = MLOptimizer()
    
    # 连接进度回调
    if progress_callback:
        optimizer.progress_updated.connect(
            lambda progress, status: progress_callback(progress, status)
        )
    
    return optimizer.optimize(xlsx_path, test_ratio, generations, pop_size, model_name)


# 导出的公共接口
__all__ = [
    'MLOptimizer',
    'optimize_parameters'
]
